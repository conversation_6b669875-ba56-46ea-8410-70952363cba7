"""
用户相关业务工具
===============

这个文件中定义的所有函数都会自动变成MCP工具！
"""
import asyncio
from typing import Dict, Any, List
from app.services.user_service import UserService


def 获取用户信息(环境: int = 1) -> Dict[str, Any]:
    """
    获取用户或企业信息
    
    :param 环境: 环境类型 1-测试环境 2-模拟环境
    :return: 用户信息
    """
    try:
        user_service = UserService()
        
        # 在新线程中运行异步函数
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(user_service.get_user_or_enterprise_info(环境))
            finally:
                loop.close()
        
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = executor.submit(run_async).result()
        
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取用户信息失败: {str(e)}"
        }


def 生成模拟用户(用户类型: str = "personal") -> Dict[str, Any]:
    """
    生成模拟用户数据
    
    :param 用户类型: 用户类型 personal/enterprise
    :return: 模拟用户数据
    """
    try:
        user_service = UserService()
        
        # 在新线程中运行异步函数
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(user_service.generate_mock_user(用户类型))
            finally:
                loop.close()
        
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = executor.submit(run_async).result()
        
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"生成模拟用户失败: {str(e)}"
        }


def 批量生成用户(数量: int = 5, 用户类型: str = "personal") -> Dict[str, Any]:
    """
    批量生成多个模拟用户
    
    :param 数量: 生成用户数量
    :param 用户类型: 用户类型 personal/enterprise
    :return: 批量用户数据
    """
    try:
        users = []
        for i in range(数量):
            user_data = 生成模拟用户(用户类型)
            if user_data.get("status") == "success":
                users.append(user_data.get("data", {}))
        
        return {
            "status": "success",
            "data": {
                "用户列表": users,
                "总数量": len(users),
                "用户类型": 用户类型
            },
            "message": f"成功生成{len(users)}个{用户类型}用户"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"批量生成用户失败: {str(e)}"
        }


def 验证用户信息(姓名: str, 身份证号: str) -> Dict[str, Any]:
    """
    验证用户信息的有效性
    
    :param 姓名: 用户姓名
    :param 身份证号: 身份证号码
    :return: 验证结果
    """
    try:
        import re
        
        # 验证身份证号格式
        id_pattern = r'^\d{17}[\dX]$'
        is_valid_id = bool(re.match(id_pattern, 身份证号))
        
        # 验证姓名格式
        name_pattern = r'^[\u4e00-\u9fa5]{2,10}$'
        is_valid_name = bool(re.match(name_pattern, 姓名))
        
        # 提取身份证信息
        birth_year = 身份证号[6:10] if is_valid_id else ""
        birth_month = 身份证号[10:12] if is_valid_id else ""
        birth_day = 身份证号[12:14] if is_valid_id else ""
        gender = "男" if is_valid_id and int(身份证号[16]) % 2 == 1 else "女"
        
        return {
            "status": "success",
            "data": {
                "姓名": 姓名,
                "身份证号": 身份证号,
                "姓名有效": is_valid_name,
                "身份证有效": is_valid_id,
                "出生年份": birth_year,
                "出生月份": birth_month,
                "出生日期": birth_day,
                "性别": gender if is_valid_id else "未知"
            },
            "message": "用户信息验证完成"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"验证用户信息失败: {str(e)}"
        }
