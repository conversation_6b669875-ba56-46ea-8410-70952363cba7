"""
测试自动生成的MCP工具
演示如何通过添加函数自动创建MCP工具
"""
import requests
import json

def test_auto_generated_tools():
    """测试自动生成的工具"""
    base_url = "http://localhost:8001/api/ai/mcp"
    
    print("🚀 测试自动生成的MCP工具")
    print("=" * 60)
    print("🎯 目标：验证新增函数是否自动生成为MCP工具")
    
    # 1. 获取工具列表
    print("\n1. 📋 获取所有自动生成的工具...")
    try:
        response = requests.get(f"{base_url}/tools")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("tools", [])
                print(f"✅ 自动发现了 {len(tools)} 个工具:")
                
                for i, tool in enumerate(tools, 1):
                    print(f"   {i}. {tool['name']}")
                    if tool['name'] in ['生成手机号', '生成身份证号', '生成银行卡号']:
                        print(f"      🆕 新增工具！")
                
                # 检查新增的工具
                new_tools = ['生成手机号', '生成身份证号', '生成银行卡号']
                found_new_tools = [tool['name'] for tool in tools if tool['name'] in new_tools]
                
                if found_new_tools:
                    print(f"\n🎉 成功！自动发现了 {len(found_new_tools)} 个新增工具:")
                    for tool_name in found_new_tools:
                        print(f"   ✅ {tool_name}")
                else:
                    print("\n❌ 没有发现新增工具")
                    return
            else:
                print(f"❌ 获取工具失败: {data.get('error')}")
                return
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 2. 测试新增的工具
    print("\n2. 🧪 测试新增的工具...")
    
    test_cases = [
        {
            "name": "生成手机号",
            "params": {"数量": 3, "运营商": "移动"},
            "description": "生成3个移动手机号"
        },
        {
            "name": "生成身份证号", 
            "params": {"数量": 2, "地区": "上海"},
            "description": "生成2个上海身份证号"
        },
        {
            "name": "生成银行卡号",
            "params": {"数量": 2, "银行": "招商银行"},
            "description": "生成2个招商银行卡号"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   测试: {test_case['description']}")
        try:
            payload = {
                "tool_name": test_case["name"],
                "parameters": test_case["params"]
            }
            
            response = requests.post(
                f"{base_url}/call",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    result = data.get("result", {})
                    if result.get("status") == "success":
                        result_data = result.get("data", {})
                        print(f"   ✅ 成功！{result.get('message')}")
                        
                        # 显示部分结果
                        if test_case["name"] == "生成手机号":
                            phones = result_data.get("手机号列表", [])
                            print(f"      📱 手机号: {phones}")
                        elif test_case["name"] == "生成身份证号":
                            ids = result_data.get("身份证号列表", [])
                            print(f"      🆔 身份证: {ids}")
                        elif test_case["name"] == "生成银行卡号":
                            cards = result_data.get("银行卡号列表", [])
                            print(f"      💳 银行卡: {cards}")
                    else:
                        print(f"   ❌ 工具执行失败: {result.get('message')}")
                else:
                    print(f"   ❌ 调用失败: {data.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 3. 测试AI对话调用新工具
    print("\n3. 🤖 测试AI自动调用新工具...")
    
    ai_test_messages = [
        "帮我生成3个移动的手机号",
        "生成2个北京的身份证号",
        "给我生成1个工商银行的银行卡号"
    ]
    
    for message in ai_test_messages:
        print(f"\n   用户: {message}")
        try:
            payload = {
                "message": message,
                "use_tools": True
            }
            
            response = requests.post(
                "http://localhost:8001/api/ai/simple-mcp/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("message"):
                    print(f"   🤖 AI: {data['message'][:100]}...")
                    
                    if data.get("tool_calls"):
                        print(f"   🔧 自动调用了工具: {[call['function'] for call in data['tool_calls']]}")
                else:
                    print(f"   ❌ AI对话失败")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 自动工具生成测试完成！")
    print("\n💡 总结:")
    print("   ✅ 只需在handlers.py中添加函数")
    print("   ✅ 系统自动生成MCP工具定义")
    print("   ✅ AI可以立即使用新工具")
    print("   ✅ 无需任何手动配置！")
    
    print("\n🚀 添加新工具的步骤:")
    print("   1. 在 app/mcp/handlers.py 中添加函数")
    print("   2. 重启服务")
    print("   3. 新工具自动生效！")

if __name__ == "__main__":
    test_auto_generated_tools()
