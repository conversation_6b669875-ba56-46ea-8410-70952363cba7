"""
AI相关API端点
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging

from app.services.ai_service import AIService
from app.schemas.ai_schemas import (
    ChatRequest, 
    ChatResponse, 
    SimpleChatRequest, 
    SimpleChatResponse,
    AvailableToolsResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/chat",
            description="AI对话接口，支持MCP工具调用",
            response_model=ChatResponse)
async def ai_chat(request: ChatRequest) -> Dict[str, Any]:
    """
    AI对话接口，支持自动调用MCP工具
    
    :param request: 聊天请求
    :return: AI响应和工具调用结果
    """
    try:
        ai_service = AIService()
        
        if request.use_tools:
            # 使用工具的对话
            conversation_history = None
            if request.conversation_history:
                conversation_history = [
                    {"role": msg.role, "content": msg.content} 
                    for msg in request.conversation_history
                ]
            
            result = await ai_service.chat_with_tools(
                message=request.message,
                conversation_history=conversation_history
            )
            return result
        else:
            # 简单对话
            message = await ai_service.simple_chat(request.message)
            return {
                "message": message,
                "tool_calls": [],
                "tool_results": []
            }
            
    except Exception as e:
        logger.error(f"AI对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI对话失败: {str(e)}")

@router.post("/simple-chat",
            description="简单AI对话接口，不使用工具",
            response_model=SimpleChatResponse)
async def simple_ai_chat(request: SimpleChatRequest) -> Dict[str, Any]:
    """
    简单AI对话接口，不调用任何工具
    
    :param request: 简单聊天请求
    :return: AI回复
    """
    try:
        ai_service = AIService()
        message = await ai_service.simple_chat(request.message)
        return {"message": message}
        
    except Exception as e:
        logger.error(f"简单AI对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"简单AI对话失败: {str(e)}")

@router.get("/tools",
           description="获取可用的MCP工具列表",
           response_model=AvailableToolsResponse)
async def get_available_tools() -> Dict[str, Any]:
    """
    获取可用的MCP工具列表
    
    :return: 可用工具列表
    """
    try:
        ai_service = AIService()
        tools = ai_service.available_tools
        
        # 格式化工具信息
        formatted_tools = []
        for tool in tools:
            function_info = tool["function"]
            formatted_tools.append({
                "name": function_info["name"],
                "description": function_info["description"],
                "parameters": function_info["parameters"]
            })
        
        return {
            "tools": formatted_tools,
            "count": len(formatted_tools)
        }
        
    except Exception as e:
        logger.error(f"获取工具列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

@router.post("/test-tool/{tool_name}",
            description="测试特定MCP工具")
async def test_mcp_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """
    测试特定的MCP工具
    
    :param tool_name: 工具名称
    :param arguments: 工具参数
    :return: 工具调用结果
    """
    try:
        ai_service = AIService()
        result = await ai_service._call_mcp_tool(tool_name, arguments)
        return {
            "tool_name": tool_name,
            "arguments": arguments,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"测试工具失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试工具失败: {str(e)}")

@router.get("/health",
           description="AI服务健康检查")
async def ai_health_check() -> Dict[str, Any]:
    """
    AI服务健康检查
    
    :return: 健康状态
    """
    try:
        ai_service = AIService()
        # 简单测试AI服务是否可用
        test_response = await ai_service.simple_chat("你好")
        
        return {
            "status": "healthy",
            "ai_service": "available",
            "test_response": test_response,
            "tools_count": len(ai_service.available_tools)
        }
        
    except Exception as e:
        logger.error(f"AI服务健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
