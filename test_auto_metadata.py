"""
测试自动元数据系统
================

验证模块元数据自动读取和系统提示词生成
"""
import requests
import json

def test_auto_metadata():
    """测试自动元数据系统"""
    print("🚀 测试自动元数据系统")
    print("=" * 60)
    print("🎯 验证目标：")
    print("   ✅ 模块元数据从业务文件自动读取")
    print("   ✅ 系统提示词包含详细的模块描述")
    print("   ✅ AI使用指南基于模块元数据生成")
    print("   ✅ 新增模块自动包含元数据")
    
    # 1. 测试工具列表是否包含模块信息
    print("\n1. 📋 检查工具列表中的模块信息...")
    try:
        response = requests.get("http://localhost:8001/api/ai/mcp/tools")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("tools", [])
                print(f"✅ 发现 {len(tools)} 个工具")
                
                # 按模块分组显示
                modules = {}
                for tool in tools:
                    module = tool.get("module", "").split('.')[-1] if tool.get("module") else "unknown"
                    if module not in modules:
                        modules[module] = []
                    modules[module].append(tool["name"])
                
                print("\n📁 模块分组:")
                for module, tool_names in modules.items():
                    print(f"   {module}: {len(tool_names)} 个工具")
                    for tool_name in tool_names[:3]:  # 只显示前3个
                        print(f"      - {tool_name}")
                    if len(tool_names) > 3:
                        print(f"      ... 还有 {len(tool_names) - 3} 个工具")
            else:
                print(f"❌ 获取工具失败: {data.get('error')}")
                return
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 2. 测试AI自我介绍（观察系统提示词效果）
    print("\n2. 🤖 测试AI自我介绍（观察新的系统提示词）...")
    
    test_messages = [
        "你好，请详细介绍一下你的功能",
        "你都有哪些模块？每个模块能做什么？",
        "你能帮我做什么？请按分类介绍"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n   测试 {i}: {message}")
        try:
            payload = {
                "message": message,
                "use_tools": False  # 不使用工具，只看AI的自我介绍
            }
            
            response = requests.post(
                "http://localhost:8001/api/ai/simple-mcp/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("message"):
                    ai_response = data["message"]
                    print(f"   🤖 AI回复长度: {len(ai_response)} 字符")
                    
                    # 检查回复中是否包含模块元数据信息
                    metadata_keywords = [
                        "用户管理", "证书管理", "数据生成", "实用工具",
                        "管理用户信息", "数字证书申请", "测试数据", "实用功能"
                    ]
                    
                    found_keywords = [kw for kw in metadata_keywords if kw in ai_response]
                    
                    if found_keywords:
                        print(f"   ✅ 包含元数据关键词: {found_keywords[:3]}...")
                        
                        # 显示部分回复内容
                        lines = ai_response.split('\n')
                        for line in lines[:8]:  # 显示前8行
                            if line.strip():
                                print(f"      {line.strip()}")
                        if len(lines) > 8:
                            print(f"      ... (还有 {len(lines) - 8} 行)")
                    else:
                        print(f"   ⚠️ 未发现元数据关键词")
                        print(f"   📄 回复预览: {ai_response[:200]}...")
                else:
                    print(f"   ❌ AI对话失败")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 3. 测试AI工具选择准确性
    print("\n3. 🎯 测试AI工具选择准确性...")
    
    targeted_tests = [
        {
            "message": "帮我生成一个用户的完整信息",
            "expected_module": "data_generator",
            "expected_tool": "生成完整用户档案"
        },
        {
            "message": "我需要验证一个身份证号是否有效",
            "expected_module": "user_tools", 
            "expected_tool": "验证用户信息"
        },
        {
            "message": "创建一个证书申请任务",
            "expected_module": "certificate_tools",
            "expected_tool": "创建证书任务"
        },
        {
            "message": "帮我生成一个强密码",
            "expected_module": "custom_tools",
            "expected_tool": "生成测试密码"
        }
    ]
    
    for test_case in targeted_tests:
        print(f"\n   测试: {test_case['message']}")
        print(f"   期望模块: {test_case['expected_module']}")
        print(f"   期望工具: {test_case['expected_tool']}")
        
        try:
            payload = {
                "message": test_case["message"],
                "use_tools": True
            }
            
            response = requests.post(
                "http://localhost:8001/api/ai/simple-mcp/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("tool_calls"):
                    tools_used = [call['function'] for call in data['tool_calls']]
                    print(f"   🔧 实际调用: {tools_used}")
                    
                    if test_case['expected_tool'] in tools_used:
                        print(f"   ✅ 工具选择正确！")
                    else:
                        print(f"   ⚠️ 工具选择可能不是最优")
                        
                    if data.get("message"):
                        print(f"   🤖 AI: {data['message'][:100]}...")
                else:
                    print(f"   ❌ AI没有调用工具")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 自动元数据系统测试完成！")
    
    print("\n💡 新系统特点:")
    print("   ✅ 模块元数据在业务文件中定义")
    print("   ✅ 系统提示词自动包含详细描述")
    print("   ✅ AI使用指南基于元数据生成")
    print("   ✅ 新增模块自动获得元数据支持")
    
    print("\n🚀 元数据定义示例:")
    print("   MODULE_META = {")
    print("       'name': '模块名称',")
    print("       'icon': '📁',")
    print("       'description': '模块功能描述',")
    print("       'category': 'core|utility',")
    print("       'ai_prompt_hint': 'AI使用提示'")
    print("   }")
    
    print("\n✨ 优势:")
    print("   ✅ 完全自动化 - 无需手动维护")
    print("   ✅ 就近定义 - 元数据和代码在一起")
    print("   ✅ 智能提示 - AI能更准确地选择工具")
    print("   ✅ 易于扩展 - 新增模块自动支持")

if __name__ == "__main__":
    test_auto_metadata()
