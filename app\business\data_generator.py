"""
数据生成业务工具
===============

这个文件中定义的所有函数都会自动变成MCP工具！
专门用于生成各种测试数据
"""
import random
from typing import Dict, Any, List
from datetime import datetime, <PERSON><PERSON><PERSON>


def 生成手机号(数量: int = 1, 运营商: str = "随机") -> Dict[str, Any]:
    """
    生成随机手机号码
    
    :param 数量: 生成手机号的数量，默认1个
    :param 运营商: 运营商类型 (移动/联通/电信/随机)，默认随机
    :return: 生成的手机号列表
    """
    try:
        # 不同运营商的号段
        mobile_prefixes = {
            "移动": ["134", "135", "136", "137", "138", "139", "147", "150", "151", "152", "157", "158", "159", "178", "182", "183", "184", "187", "188", "198"],
            "联通": ["130", "131", "132", "145", "155", "156", "166", "171", "175", "176", "185", "186", "196"],
            "电信": ["133", "149", "153", "173", "177", "180", "181", "189", "191", "193", "199"]
        }
        
        # 选择号段
        if 运营商 == "随机":
            all_prefixes = []
            for prefixes in mobile_prefixes.values():
                all_prefixes.extend(prefixes)
            prefixes = all_prefixes
        else:
            prefixes = mobile_prefixes.get(运营商, mobile_prefixes["移动"])
        
        # 生成手机号
        phone_numbers = []
        for _ in range(数量):
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            phone_number = prefix + suffix
            phone_numbers.append(phone_number)
        
        return {
            "status": "success",
            "data": {
                "手机号列表": phone_numbers,
                "数量": len(phone_numbers),
                "运营商": 运营商
            },
            "message": f"成功生成{len(phone_numbers)}个{运营商}手机号"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"生成手机号失败: {str(e)}"
        }


def 生成身份证号(数量: int = 1, 地区: str = "北京") -> Dict[str, Any]:
    """
    生成随机身份证号码
    
    :param 数量: 生成身份证号的数量，默认1个
    :param 地区: 地区名称，默认北京
    :return: 生成的身份证号列表
    """
    try:
        # 地区代码映射
        area_codes = {
            "北京": "110101",
            "上海": "310101", 
            "广州": "440101",
            "深圳": "440301",
            "杭州": "330101",
            "南京": "320101",
            "成都": "510101",
            "重庆": "500101"
        }
        
        area_code = area_codes.get(地区, "110101")
        
        id_numbers = []
        for _ in range(数量):
            # 生成出生日期 (1970-2000年)
            start_date = datetime(1970, 1, 1)
            end_date = datetime(2000, 12, 31)
            random_date = start_date + timedelta(
                days=random.randint(0, (end_date - start_date).days)
            )
            birth_date = random_date.strftime("%Y%m%d")
            
            # 生成顺序码 (奇数为男，偶数为女)
            sequence = str(random.randint(100, 999))
            
            # 前17位
            id_17 = area_code + birth_date + sequence
            
            # 计算校验码
            weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
            check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
            
            sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
            check_code = check_codes[sum_val % 11]
            
            id_number = id_17 + check_code
            id_numbers.append(id_number)
        
        return {
            "status": "success",
            "data": {
                "身份证号列表": id_numbers,
                "数量": len(id_numbers),
                "地区": 地区
            },
            "message": f"成功生成{len(id_numbers)}个{地区}身份证号"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"生成身份证号失败: {str(e)}"
        }


def 生成银行卡号(数量: int = 1, 银行: str = "工商银行") -> Dict[str, Any]:
    """
    生成随机银行卡号
    
    :param 数量: 生成银行卡号的数量，默认1个
    :param 银行: 银行名称，默认工商银行
    :return: 生成的银行卡号列表
    """
    try:
        # 银行卡号前缀
        bank_prefixes = {
            "工商银行": ["622202", "622208", "622210", "622211"],
            "建设银行": ["436742", "622280", "622700", "622708"],
            "农业银行": ["622848", "622849", "622850", "622851"],
            "中国银行": ["456351", "601382", "622760", "622761"],
            "招商银行": ["622575", "622576", "622577", "622578"],
            "交通银行": ["622260", "622261", "622262", "622263"],
            "民生银行": ["622600", "622601", "622602", "622603"]
        }
        
        prefixes = bank_prefixes.get(银行, bank_prefixes["工商银行"])
        
        card_numbers = []
        for _ in range(数量):
            prefix = random.choice(prefixes)
            # 生成剩余位数 (通常银行卡号为16-19位)
            remaining_digits = 19 - len(prefix)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(remaining_digits)])
            card_number = prefix + suffix
            card_numbers.append(card_number)
        
        return {
            "status": "success",
            "data": {
                "银行卡号列表": card_numbers,
                "数量": len(card_numbers),
                "银行": 银行
            },
            "message": f"成功生成{len(card_numbers)}个{银行}银行卡号"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"生成银行卡号失败: {str(e)}"
        }


def 生成邮箱地址(数量: int = 1, 域名: str = "随机") -> Dict[str, Any]:
    """
    生成随机邮箱地址

    :param 数量: 生成邮箱的数量，默认1个
    :param 域名: 邮箱域名 (qq.com/163.com/gmail.com/随机)，默认随机
    :return: 生成的邮箱列表
    """
    try:
        domains = ["qq.com", "163.com", "gmail.com", "126.com", "sina.com", "hotmail.com"]

        if 域名 != "随机" and 域名 in domains:
            selected_domains = [域名]
        else:
            selected_domains = domains

        emails = []
        for _ in range(数量):
            # 生成用户名
            username_length = random.randint(6, 12)
            username = ''.join([random.choice('abcdefghijklmnopqrstuvwxyz0123456789') for _ in range(username_length)])

            # 选择域名
            domain = random.choice(selected_domains)

            email = f"{username}@{domain}"
            emails.append(email)

        return {
            "status": "success",
            "data": {
                "邮箱列表": emails,
                "数量": len(emails),
                "域名": 域名
            },
            "message": f"成功生成{len(emails)}个邮箱地址"
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"生成邮箱地址失败: {str(e)}"
        }


def 生成完整用户档案(数量: int = 1, 地区: str = "北京") -> Dict[str, Any]:
    """
    生成完整的用户档案信息

    :param 数量: 生成档案的数量，默认1个
    :param 地区: 地区名称，默认北京
    :return: 完整用户档案列表
    """
    try:
        profiles = []

        for _ in range(数量):
            # 生成各种信息
            phone_result = 生成手机号(1, "随机")
            id_result = 生成身份证号(1, 地区)
            card_result = 生成银行卡号(1, "工商银行")
            email_result = 生成邮箱地址(1, "随机")

            # 生成姓名
            surnames = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴"]
            given_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋"]
            name = random.choice(surnames) + random.choice(given_names)

            profile = {
                "姓名": name,
                "手机号": phone_result["data"]["手机号列表"][0] if phone_result["status"] == "success" else "",
                "身份证号": id_result["data"]["身份证号列表"][0] if id_result["status"] == "success" else "",
                "银行卡号": card_result["data"]["银行卡号列表"][0] if card_result["status"] == "success" else "",
                "邮箱": email_result["data"]["邮箱列表"][0] if email_result["status"] == "success" else "",
                "地区": 地区
            }
            profiles.append(profile)

        return {
            "status": "success",
            "data": {
                "用户档案列表": profiles,
                "数量": len(profiles),
                "地区": 地区
            },
            "message": f"成功生成{len(profiles)}个完整用户档案"
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"生成完整用户档案失败: {str(e)}"
        }
