"""
MCP工具处理器
使用服务层来处理业务逻辑
"""
from typing import Any, Optional, List, Dict
import logging
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

from app.services.certificate_service import CertificateService
from app.services.user_service import UserService

logger = logging.getLogger(__name__)

# 创建线程池执行器
executor = ThreadPoolExecutor(max_workers=4)

def run_async_in_thread(coro):
    """在新线程中运行异步函数"""
    def run_in_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    future = executor.submit(run_in_thread)
    return future.result()

def 创建有账号证书申请任务接口(个人or企业: int = 1, oid: Optional[str] = None, 产品: List[int] = [1], 使用老数据: int = 1) -> Dict[str, Any]:
    """
    创建有账号的证书申请任务

    :param 个人or企业: 用户类型 1-个人 2-企业
    :param oid: 如果传入oid，则不使用姓名和证件号
    :param 产品: 枚举 [1-TIANYIN, 2-TCLOUD, 3-ESHIELD]
    :param 使用老数据: 0-不使用老数据，1-使用老数据
    :return: 返回任务ID等信息
    """
    try:
        certificate_service = CertificateService()
        # 使用线程池运行异步函数，避免事件循环冲突
        result = run_async_in_thread(certificate_service.create_certificate_task(
            user_type=个人or企业,
            oid=oid,
            products=产品,
            use_old_data=使用老数据
        ))
        return result
    except Exception as e:
        logger.error(f"创建证书任务失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 根据taskId查询任务状态(taskId: str) -> Dict[str, Any]:
    """
    查询任务状态

    :param taskId: 任务ID
    :return: 返回任务状态信息
    """
    try:
        certificate_service = CertificateService()
        # 使用线程池运行异步函数，避免事件循环冲突
        result = run_async_in_thread(certificate_service.get_task_status(taskId))
        return result
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 获取用户或企业信息(环境: int = 1) -> Dict[str, Any]:
    """
    获取用户或企业信息

    :param 环境: 环境类型 1-测试环境 2-模拟环境
    :return: 用户信息
    """
    try:
        user_service = UserService()
        # 使用线程池运行异步函数，避免事件循环冲突
        result = run_async_in_thread(user_service.get_user_or_enterprise_info(环境))
        return result
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 生成模拟用户数据(用户类型: str = "personal") -> Dict[str, Any]:
    """
    生成模拟用户数据

    :param 用户类型: 用户类型 personal/enterprise
    :return: 模拟用户数据
    """
    try:
        user_service = UserService()
        # 使用线程池运行异步函数，避免事件循环冲突
        result = run_async_in_thread(user_service.generate_mock_user(用户类型))
        return result
    except Exception as e:
        logger.error(f"生成模拟用户数据失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

# ============= 🚀 新增工具演示 =============
# 只需要在这里添加函数，系统会自动生成MCP工具！

def 生成手机号(数量: int = 1, 运营商: str = "随机") -> Dict[str, Any]:
    """
    生成随机手机号码

    :param 数量: 生成手机号的数量，默认1个
    :param 运营商: 运营商类型 (移动/联通/电信/随机)，默认随机
    :return: 生成的手机号列表
    """
    import random

    try:
        # 不同运营商的号段
        mobile_prefixes = {
            "移动": ["134", "135", "136", "137", "138", "139", "147", "150", "151", "152", "157", "158", "159", "178", "182", "183", "184", "187", "188", "198"],
            "联通": ["130", "131", "132", "145", "155", "156", "166", "171", "175", "176", "185", "186", "196"],
            "电信": ["133", "149", "153", "173", "177", "180", "181", "189", "191", "193", "199"]
        }

        # 选择号段
        if 运营商 == "随机":
            all_prefixes = []
            for prefixes in mobile_prefixes.values():
                all_prefixes.extend(prefixes)
            prefixes = all_prefixes
        else:
            prefixes = mobile_prefixes.get(运营商, mobile_prefixes["移动"])

        # 生成手机号
        phone_numbers = []
        for _ in range(数量):
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            phone_number = prefix + suffix
            phone_numbers.append(phone_number)

        return {
            "status": "success",
            "data": {
                "手机号列表": phone_numbers,
                "数量": len(phone_numbers),
                "运营商": 运营商
            },
            "message": f"成功生成{len(phone_numbers)}个{运营商}手机号"
        }

    except Exception as e:
        logger.error(f"生成手机号失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 生成身份证号(数量: int = 1, 地区: str = "北京") -> Dict[str, Any]:
    """
    生成随机身份证号码

    :param 数量: 生成身份证号的数量，默认1个
    :param 地区: 地区名称，默认北京
    :return: 生成的身份证号列表
    """
    import random
    from datetime import datetime, timedelta

    try:
        # 地区代码映射
        area_codes = {
            "北京": "110101",
            "上海": "310101",
            "广州": "440101",
            "深圳": "440301",
            "杭州": "330101",
            "南京": "320101"
        }

        area_code = area_codes.get(地区, "110101")

        id_numbers = []
        for _ in range(数量):
            # 生成出生日期 (1970-2000年)
            start_date = datetime(1970, 1, 1)
            end_date = datetime(2000, 12, 31)
            random_date = start_date + timedelta(
                days=random.randint(0, (end_date - start_date).days)
            )
            birth_date = random_date.strftime("%Y%m%d")

            # 生成顺序码 (奇数为男，偶数为女)
            sequence = str(random.randint(100, 999))

            # 前17位
            id_17 = area_code + birth_date + sequence

            # 计算校验码
            weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
            check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

            sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
            check_code = check_codes[sum_val % 11]

            id_number = id_17 + check_code
            id_numbers.append(id_number)

        return {
            "status": "success",
            "data": {
                "身份证号列表": id_numbers,
                "数量": len(id_numbers),
                "地区": 地区
            },
            "message": f"成功生成{len(id_numbers)}个{地区}身份证号"
        }

    except Exception as e:
        logger.error(f"生成身份证号失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 生成银行卡号(数量: int = 1, 银行: str = "工商银行") -> Dict[str, Any]:
    """
    生成随机银行卡号

    :param 数量: 生成银行卡号的数量，默认1个
    :param 银行: 银行名称，默认工商银行
    :return: 生成的银行卡号列表
    """
    import random

    try:
        # 银行卡号前缀
        bank_prefixes = {
            "工商银行": ["622202", "622208", "622210", "622211"],
            "建设银行": ["436742", "622280", "622700", "622708"],
            "农业银行": ["622848", "622849", "622850", "622851"],
            "中国银行": ["456351", "601382", "622760", "622761"],
            "招商银行": ["622575", "622576", "622577", "622578"]
        }

        prefixes = bank_prefixes.get(银行, bank_prefixes["工商银行"])

        card_numbers = []
        for _ in range(数量):
            prefix = random.choice(prefixes)
            # 生成剩余位数 (通常银行卡号为16-19位)
            remaining_digits = 19 - len(prefix)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(remaining_digits)])
            card_number = prefix + suffix
            card_numbers.append(card_number)

        return {
            "status": "success",
            "data": {
                "银行卡号列表": card_numbers,
                "数量": len(card_numbers),
                "银行": 银行
            },
            "message": f"成功生成{len(card_numbers)}个{银行}银行卡号"
        }

    except Exception as e:
        logger.error(f"生成银行卡号失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

# MCP工具注册函数（如果需要的话）
def register_mcp_tools(mcp):
    """注册MCP工具"""
    mcp.tool(description="创建有账号证书申请任务接口，make cert task with accountId")(创建有账号证书申请任务接口)
    mcp.tool(description="search task status，根据taskId查询任务状态")(根据taskId查询任务状态)
    mcp.tool(description="获取用户或企业信息，get user or enterprise info")(获取用户或企业信息)
    mcp.tool(description="生成模拟用户数据，generate mock user data")(生成模拟用户数据)