"""
MCP工具处理器
使用服务层来处理业务逻辑
"""
from typing import Any, Optional, List, Dict
import logging
import asyncio

from app.services.certificate_service import CertificateService
from app.services.user_service import UserService

logger = logging.getLogger(__name__)

def 创建有账号证书申请任务接口(个人or企业: int = 1, oid: Optional[str] = None, 产品: List[int] = [1], 使用老数据: int = 1) -> Dict[str, Any]:
    """
    创建有账号的证书申请任务

    :param 个人or企业: 用户类型 1-个人 2-企业
    :param oid: 如果传入oid，则不使用姓名和证件号
    :param 产品: 枚举 [1-TIANYIN, 2-TCLOUD, 3-ESHIELD]
    :param 使用老数据: 0-不使用老数据，1-使用老数据
    :return: 返回任务ID等信息
    """
    try:
        certificate_service = CertificateService()
        # 检查是否在事件循环中
        try:
            loop = asyncio.get_running_loop()
            # 如果在事件循环中，创建任务
            import asyncio
            future = asyncio.ensure_future(certificate_service.create_certificate_task(
                user_type=个人or企业,
                oid=oid,
                products=产品,
                use_old_data=使用老数据
            ))
            # 等待完成
            while not future.done():
                pass
            result = future.result()
        except RuntimeError:
            # 没有运行的事件循环，使用asyncio.run
            result = asyncio.run(certificate_service.create_certificate_task(
                user_type=个人or企业,
                oid=oid,
                products=产品,
                use_old_data=使用老数据
            ))
        return result
    except Exception as e:
        logger.error(f"创建证书任务失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 根据taskId查询任务状态(taskId: str) -> Dict[str, Any]:
    """
    查询任务状态

    :param taskId: 任务ID
    :return: 返回任务状态信息
    """
    try:
        certificate_service = CertificateService()
        # 由于MCP工具不支持async，使用asyncio.run
        result = asyncio.run(certificate_service.get_task_status(taskId))
        return result
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 获取用户或企业信息(环境: int = 1) -> Dict[str, Any]:
    """
    获取用户或企业信息

    :param 环境: 环境类型 1-测试环境 2-模拟环境
    :return: 用户信息
    """
    try:
        user_service = UserService()
        # 由于MCP工具不支持async，使用asyncio.run
        result = asyncio.run(user_service.get_user_or_enterprise_info(环境))
        return result
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

def 生成模拟用户数据(用户类型: str = "personal") -> Dict[str, Any]:
    """
    生成模拟用户数据

    :param 用户类型: 用户类型 personal/enterprise
    :return: 模拟用户数据
    """
    try:
        user_service = UserService()
        # 由于MCP工具不支持async，使用asyncio.run
        result = asyncio.run(user_service.generate_mock_user(用户类型))
        return result
    except Exception as e:
        logger.error(f"生成模拟用户数据失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

# MCP工具注册函数（如果需要的话）
def register_mcp_tools(mcp):
    """注册MCP工具"""
    mcp.tool(description="创建有账号证书申请任务接口，make cert task with accountId")(创建有账号证书申请任务接口)
    mcp.tool(description="search task status，根据taskId查询任务状态")(根据taskId查询任务状态)
    mcp.tool(description="获取用户或企业信息，get user or enterprise info")(获取用户或企业信息)
    mcp.tool(description="生成模拟用户数据，generate mock user data")(生成模拟用户数据)