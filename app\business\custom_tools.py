"""
自定义业务工具
=============

这个文件中定义的所有函数都会自动变成MCP工具！
您可以在这里添加任何自定义的业务方法
"""

# 🚀 模块元数据 - 用于自动生成系统提示词
MODULE_META = {
    "name": "实用工具",
    "icon": "⚙️",
    "description": "提供各种实用功能，包括密码生成、JSON格式化、文本分析、时间计算、颜色生成等",
    "category": "utility",
    "ai_prompt_hint": "当用户需要实用工具时，如生成密码、格式化数据、分析文本、计算时间差、生成颜色等，请使用此模块的工具"
}
import random
import json
from typing import Dict, Any, List
from datetime import datetime


def 生成测试密码(数量: int = 1, 长度: int = 8, 包含特殊字符: bool = True) -> Dict[str, Any]:
    """
    生成测试用密码
    
    :param 数量: 生成密码的数量，默认1个
    :param 长度: 密码长度，默认8位
    :param 包含特殊字符: 是否包含特殊字符，默认True
    :return: 生成的密码列表
    """
    try:
        import string
        
        # 字符集
        letters = string.ascii_letters  # a-z, A-Z
        digits = string.digits  # 0-9
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?" if 包含特殊字符 else ""
        
        all_chars = letters + digits + special_chars
        
        passwords = []
        for _ in range(数量):
            password = ''.join(random.choice(all_chars) for _ in range(长度))
            passwords.append(password)
        
        return {
            "status": "success",
            "data": {
                "密码列表": passwords,
                "数量": len(passwords),
                "长度": 长度,
                "包含特殊字符": 包含特殊字符
            },
            "message": f"成功生成{len(passwords)}个密码"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"生成密码失败: {str(e)}"
        }


def 格式化JSON数据(json_字符串: str) -> Dict[str, Any]:
    """
    格式化JSON数据，使其更易读
    
    :param json_字符串: 需要格式化的JSON字符串
    :return: 格式化后的JSON
    """
    try:
        # 解析JSON
        data = json.loads(json_字符串)
        
        # 格式化输出
        formatted_json = json.dumps(data, ensure_ascii=False, indent=2)
        
        return {
            "status": "success",
            "data": {
                "原始JSON": json_字符串,
                "格式化JSON": formatted_json,
                "数据类型": type(data).__name__
            },
            "message": "JSON格式化成功"
        }
        
    except json.JSONDecodeError as e:
        return {
            "status": "error",
            "message": f"JSON格式错误: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"格式化JSON失败: {str(e)}"
        }


def 计算时间差(开始时间: str, 结束时间: str, 时间格式: str = "%Y-%m-%d %H:%M:%S") -> Dict[str, Any]:
    """
    计算两个时间之间的差值
    
    :param 开始时间: 开始时间字符串
    :param 结束时间: 结束时间字符串
    :param 时间格式: 时间格式，默认 "%Y-%m-%d %H:%M:%S"
    :return: 时间差计算结果
    """
    try:
        # 解析时间
        start_dt = datetime.strptime(开始时间, 时间格式)
        end_dt = datetime.strptime(结束时间, 时间格式)
        
        # 计算差值
        time_diff = end_dt - start_dt
        
        # 提取各种时间单位
        total_seconds = int(time_diff.total_seconds())
        days = time_diff.days
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        return {
            "status": "success",
            "data": {
                "开始时间": 开始时间,
                "结束时间": 结束时间,
                "总秒数": total_seconds,
                "天数": days,
                "小时数": hours,
                "分钟数": minutes,
                "秒数": seconds,
                "时间差描述": f"{days}天{hours}小时{minutes}分钟{seconds}秒"
            },
            "message": "时间差计算成功"
        }
        
    except ValueError as e:
        return {
            "status": "error",
            "message": f"时间格式错误: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"计算时间差失败: {str(e)}"
        }


def 生成随机颜色(数量: int = 1, 格式: str = "hex") -> Dict[str, Any]:
    """
    生成随机颜色值
    
    :param 数量: 生成颜色的数量，默认1个
    :param 格式: 颜色格式 (hex/rgb/hsl)，默认hex
    :return: 生成的颜色列表
    """
    try:
        colors = []
        
        for _ in range(数量):
            # 生成RGB值
            r = random.randint(0, 255)
            g = random.randint(0, 255)
            b = random.randint(0, 255)
            
            if 格式.lower() == "hex":
                color = f"#{r:02x}{g:02x}{b:02x}"
            elif 格式.lower() == "rgb":
                color = f"rgb({r}, {g}, {b})"
            elif 格式.lower() == "hsl":
                # 转换为HSL
                r_norm = r / 255.0
                g_norm = g / 255.0
                b_norm = b / 255.0
                
                max_val = max(r_norm, g_norm, b_norm)
                min_val = min(r_norm, g_norm, b_norm)
                
                h = s = l = (max_val + min_val) / 2
                
                if max_val == min_val:
                    h = s = 0  # 灰色
                else:
                    d = max_val - min_val
                    s = d / (2 - max_val - min_val) if l > 0.5 else d / (max_val + min_val)
                    
                    if max_val == r_norm:
                        h = (g_norm - b_norm) / d + (6 if g_norm < b_norm else 0)
                    elif max_val == g_norm:
                        h = (b_norm - r_norm) / d + 2
                    elif max_val == b_norm:
                        h = (r_norm - g_norm) / d + 4
                    h /= 6
                
                color = f"hsl({int(h*360)}, {int(s*100)}%, {int(l*100)}%)"
            else:
                color = f"#{r:02x}{g:02x}{b:02x}"  # 默认hex
            
            colors.append(color)
        
        return {
            "status": "success",
            "data": {
                "颜色列表": colors,
                "数量": len(colors),
                "格式": 格式
            },
            "message": f"成功生成{len(colors)}个{格式}格式颜色"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"生成颜色失败: {str(e)}"
        }


def 文本统计分析(文本内容: str) -> Dict[str, Any]:
    """
    分析文本内容的统计信息
    
    :param 文本内容: 需要分析的文本
    :return: 文本统计结果
    """
    try:
        import re
        
        # 基本统计
        字符总数 = len(文本内容)
        字符数_不含空格 = len(文本内容.replace(' ', ''))
        行数 = len(文本内容.split('\n'))
        段落数 = len([p for p in 文本内容.split('\n\n') if p.strip()])
        
        # 词语统计
        words = re.findall(r'\b\w+\b', 文本内容.lower())
        单词总数 = len(words)
        唯一单词数 = len(set(words))
        
        # 中文字符统计
        中文字符 = re.findall(r'[\u4e00-\u9fff]', 文本内容)
        中文字符数 = len(中文字符)
        
        # 数字统计
        数字 = re.findall(r'\d', 文本内容)
        数字个数 = len(数字)
        
        # 标点符号统计
        标点符号 = re.findall(r'[^\w\s\u4e00-\u9fff]', 文本内容)
        标点符号数 = len(标点符号)
        
        return {
            "status": "success",
            "data": {
                "字符总数": 字符总数,
                "字符数_不含空格": 字符数_不含空格,
                "行数": 行数,
                "段落数": 段落数,
                "单词总数": 单词总数,
                "唯一单词数": 唯一单词数,
                "中文字符数": 中文字符数,
                "数字个数": 数字个数,
                "标点符号数": 标点符号数,
                "平均每行字符数": round(字符总数 / 行数, 2) if 行数 > 0 else 0
            },
            "message": "文本统计分析完成"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"文本统计分析失败: {str(e)}"
        }
