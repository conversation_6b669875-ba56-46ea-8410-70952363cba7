"""
证书相关业务工具
===============

这个文件中定义的所有函数都会自动变成MCP工具！
"""

# 🚀 模块元数据 - 用于自动生成系统提示词
MODULE_META = {
    "name": "证书管理",
    "icon": "📜",
    "description": "管理数字证书申请、查询、批量处理等功能",
    "category": "core",
    "ai_prompt_hint": "当用户需要证书相关操作时，如创建证书任务、查询任务状态、获取证书产品信息等，请使用此模块的工具"
}
import asyncio
from typing import Dict, Any, List
from app.services.certificate_service import CertificateService


def 创建证书任务(用户类型: int = 1, oid: str = None, 产品列表: List[int] = None, 使用老数据: int = 1) -> Dict[str, Any]:
    """
    创建证书申请任务
    
    :param 用户类型: 用户类型 1-个人 2-企业
    :param oid: 用户OID，如果传入则不使用姓名和证件号
    :param 产品列表: 产品列表 [1-TIANYIN, 2-TCLOUD, 3-ESHIELD]
    :param 使用老数据: 是否使用老数据 0-不使用 1-使用
    :return: 任务创建结果
    """
    try:
        if 产品列表 is None:
            产品列表 = [1]
            
        certificate_service = CertificateService()
        
        # 在新线程中运行异步函数
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(certificate_service.create_certificate_task(
                    user_type=用户类型,
                    oid=oid,
                    products=产品列表,
                    use_old_data=使用老数据
                ))
            finally:
                loop.close()
        
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = executor.submit(run_async).result()
        
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"创建证书任务失败: {str(e)}"
        }


def 查询任务状态(任务ID: str) -> Dict[str, Any]:
    """
    查询证书任务状态
    
    :param 任务ID: 证书任务ID
    :return: 任务状态信息
    """
    try:
        certificate_service = CertificateService()
        
        # 在新线程中运行异步函数
        def run_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(certificate_service.get_task_status(任务ID))
            finally:
                loop.close()
        
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = executor.submit(run_async).result()
        
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"查询任务状态失败: {str(e)}"
        }


def 批量创建证书任务(任务数量: int = 3, 用户类型: int = 1, 产品列表: List[int] = None) -> Dict[str, Any]:
    """
    批量创建多个证书任务
    
    :param 任务数量: 创建任务的数量
    :param 用户类型: 用户类型 1-个人 2-企业
    :param 产品列表: 产品列表 [1-TIANYIN, 2-TCLOUD, 3-ESHIELD]
    :return: 批量任务创建结果
    """
    try:
        if 产品列表 is None:
            产品列表 = [1]
            
        tasks = []
        for i in range(任务数量):
            task_result = 创建证书任务(用户类型, None, 产品列表, 1)
            if task_result.get("status") == "success":
                tasks.append(task_result.get("data", {}))
        
        return {
            "status": "success",
            "data": {
                "任务列表": tasks,
                "成功数量": len(tasks),
                "总数量": 任务数量,
                "用户类型": "个人" if 用户类型 == 1 else "企业"
            },
            "message": f"成功创建{len(tasks)}个证书任务"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"批量创建证书任务失败: {str(e)}"
        }


def 获取证书产品信息() -> Dict[str, Any]:
    """
    获取可用的证书产品信息
    
    :return: 证书产品列表
    """
    try:
        products = {
            1: {
                "名称": "TIANYIN",
                "描述": "天印数字证书",
                "类型": "个人/企业证书"
            },
            2: {
                "名称": "TCLOUD", 
                "描述": "天威云证书",
                "类型": "云端证书服务"
            },
            3: {
                "名称": "ESHIELD",
                "描述": "电子盾证书",
                "类型": "安全防护证书"
            }
        }
        
        return {
            "status": "success",
            "data": {
                "产品列表": products,
                "产品数量": len(products)
            },
            "message": "获取证书产品信息成功"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取证书产品信息失败: {str(e)}"
        }
