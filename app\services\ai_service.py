"""
AI服务 - 集成DeepSeek模型和MCP工具调用
"""
import json
import logging
import inspect
from typing import Dict, Any, List, Optional
from openai import OpenAI

from app.core.config import settings
from app.services.base_service import BaseService
from app.mcp import handlers

class AIService(BaseService):
    """AI服务类，处理AI对话和MCP工具调用"""
    
    def __init__(self):
        super().__init__()
        self.client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )

        # 🚀 自动生成MCP工具定义 - 无需手动配置！
        self.available_tools = self._auto_generate_tools()

    def _auto_generate_tools(self) -> List[Dict[str, Any]]:
        """
        自动从handlers模块生成OpenAI工具定义

        🎯 实现目标：
        1. 自动发现handlers中的所有函数
        2. 自动解析函数签名和类型注解
        3. 自动生成OpenAI工具格式
        4. 新增函数自动生效，无需手动配置

        :return: OpenAI工具定义列表
        """
        tools = []

        # 自动发现handlers模块中的所有函数
        for name, func in inspect.getmembers(handlers, inspect.isfunction):
            # 跳过私有函数和辅助函数
            if name.startswith('_') or name in ['register_mcp_tools', 'run_async_in_thread']:
                continue

            # 获取函数签名
            sig = inspect.signature(func)

            # 构建OpenAI工具格式
            tool_def = {
                "type": "function",
                "function": {
                    "name": name,
                    "description": func.__doc__ or f"调用{name}函数",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }

            # 自动解析参数
            for param_name, param in sig.parameters.items():
                param_info = self._parse_parameter(param)
                tool_def["function"]["parameters"]["properties"][param_name] = param_info

                # 如果参数没有默认值，则为必需参数
                if param.default == inspect.Parameter.empty:
                    tool_def["function"]["parameters"]["required"].append(param_name)

            tools.append(tool_def)
            self.logger.info(f"🔧 自动生成工具: {name}")

        self.logger.info(f"🎉 自动生成了 {len(tools)} 个MCP工具")
        return tools

    def _parse_parameter(self, param: inspect.Parameter) -> Dict[str, Any]:
        """
        解析函数参数，生成OpenAI参数定义

        :param param: 函数参数
        :return: OpenAI参数定义
        """
        param_info = {
            "type": "string",  # 默认类型
            "description": f"参数 {param.name}"
        }

        # 根据类型注解设置参数类型
        if param.annotation != inspect.Parameter.empty:
            if param.annotation == int:
                param_info["type"] = "integer"
            elif param.annotation == float:
                param_info["type"] = "number"
            elif param.annotation == bool:
                param_info["type"] = "boolean"
            elif param.annotation == list or str(param.annotation).startswith('typing.List'):
                param_info["type"] = "array"
            elif param.annotation == dict or str(param.annotation).startswith('typing.Dict'):
                param_info["type"] = "object"

        # 设置默认值
        if param.default != inspect.Parameter.empty:
            param_info["default"] = param.default

        return param_info
    
    async def chat_with_tools(
        self, 
        message: str, 
        conversation_history: List[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        与AI对话，支持工具调用
        
        :param message: 用户消息
        :param conversation_history: 对话历史
        :return: AI响应和工具调用结果
        """
        try:
            # 构建消息历史
            messages = []
            
            # 系统提示
            system_prompt = """你是一个专业的证书管理助手，可以帮助用户：
1. 创建证书申请任务
2. 查询任务状态
3. 获取用户或企业信息
4. 生成模拟用户数据

当用户需要这些功能时，请主动调用相应的工具。请用中文回复用户。"""
            
            messages.append({"role": "system", "content": system_prompt})
            
            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            self.logger.info(f"发送消息到DeepSeek: {message}")
            
            # 调用AI模型
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=messages,
                tools=self.available_tools,
                tool_choice="auto",
                temperature=0.7
            )
            
            assistant_message = response.choices[0].message
            tool_calls = assistant_message.tool_calls
            
            result = {
                "message": assistant_message.content,
                "tool_calls": [],
                "tool_results": []
            }
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    self.logger.info(f"调用工具: {function_name}, 参数: {function_args}")
                    
                    # 调用对应的MCP工具
                    tool_result = await self._call_mcp_tool(function_name, function_args)
                    
                    result["tool_calls"].append({
                        "function": function_name,
                        "arguments": function_args
                    })
                    result["tool_results"].append(tool_result)
                
                # 如果有工具调用，让AI总结结果
                if result["tool_results"]:
                    summary_messages = messages + [
                        {"role": "assistant", "content": assistant_message.content},
                        {"role": "user", "content": f"工具调用结果: {json.dumps(result['tool_results'], ensure_ascii=False)}，请总结一下结果。"}
                    ]
                    
                    summary_response = self.client.chat.completions.create(
                        model=settings.DEEPSEEK_MODEL,
                        messages=summary_messages,
                        temperature=0.7
                    )
                    
                    result["message"] = summary_response.choices[0].message.content
            
            self.logger.info(f"AI响应完成")
            return result
            
        except Exception as e:
            self.logger.error(f"AI对话失败: {str(e)}")
            raise
    
    async def _call_mcp_tool(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具

        :param function_name: 函数名
        :param arguments: 参数
        :return: 工具调用结果
        """
        try:
            # 直接调用服务层，避免MCP handlers的异步问题
            if function_name == "create_certificate_task":
                from app.services.certificate_service import CertificateService
                service = CertificateService()
                return await service.create_certificate_task(
                    user_type=arguments.get("user_type", 1),
                    oid=arguments.get("oid"),
                    products=arguments.get("products", [1]),
                    use_old_data=arguments.get("use_old_data", 1)
                )
            elif function_name == "query_task_status":
                from app.services.certificate_service import CertificateService
                service = CertificateService()
                return await service.get_task_status(arguments["task_id"])
            elif function_name == "get_user_info":
                from app.services.user_service import UserService
                service = UserService()
                return await service.get_user_or_enterprise_info(arguments.get("env", 1))
            elif function_name == "generate_mock_user":
                from app.services.user_service import UserService
                service = UserService()
                return await service.generate_mock_user(arguments.get("user_type", "personal"))
            else:
                return {"error": f"未知的工具: {function_name}"}

        except Exception as e:
            self.logger.error(f"调用MCP工具失败: {function_name}, 错误: {str(e)}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    async def simple_chat(self, message: str) -> str:
        """
        简单对话，不使用工具
        
        :param message: 用户消息
        :return: AI回复
        """
        try:
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=[
                    {"role": "system", "content": "你是一个友好的助手，请用中文回复。"},
                    {"role": "user", "content": message}
                ],
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"简单对话失败: {str(e)}")
            raise
