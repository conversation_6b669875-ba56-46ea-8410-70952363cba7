"""
AI服务 - 集成DeepSeek模型和MCP工具调用
"""
import json
import logging
from typing import Dict, Any, List, Optional
from openai import OpenAI

from app.core.config import settings
from app.services.base_service import BaseService
from app.mcp.handlers import (
    创建有账号证书申请任务接口,
    根据taskId查询任务状态,
    获取用户或企业信息,
    生成模拟用户数据
)

class AIService(BaseService):
    """AI服务类，处理AI对话和MCP工具调用"""
    
    def __init__(self):
        super().__init__()
        self.client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        
        # 定义可用的MCP工具
        self.available_tools = [
            {
                "type": "function",
                "function": {
                    "name": "create_certificate_task",
                    "description": "创建有账号的证书申请任务",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "user_type": {
                                "type": "integer",
                                "description": "用户类型 1-个人 2-企业",
                                "enum": [1, 2]
                            },
                            "oid": {
                                "type": "string",
                                "description": "如果传入oid，则不使用姓名和证件号"
                            },
                            "products": {
                                "type": "array",
                                "items": {"type": "integer"},
                                "description": "产品列表 [1-TIANYIN, 2-TCLOUD, 3-ESHIELD]"
                            },
                            "use_old_data": {
                                "type": "integer",
                                "description": "是否使用老数据 0-不使用 1-使用",
                                "enum": [0, 1]
                            }
                        },
                        "required": ["user_type"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "query_task_status",
                    "description": "查询证书任务状态",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "task_id": {
                                "type": "string",
                                "description": "任务ID"
                            }
                        },
                        "required": ["task_id"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_user_info",
                    "description": "获取用户或企业信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "env": {
                                "type": "integer",
                                "description": "环境类型 1-测试环境 2-模拟环境",
                                "enum": [1, 2]
                            }
                        },
                        "required": ["env"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "generate_mock_user",
                    "description": "生成模拟用户数据",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "user_type": {
                                "type": "string",
                                "description": "用户类型 personal/enterprise",
                                "enum": ["personal", "enterprise"]
                            }
                        },
                        "required": ["user_type"]
                    }
                }
            }
        ]
    
    async def chat_with_tools(
        self, 
        message: str, 
        conversation_history: List[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        与AI对话，支持工具调用
        
        :param message: 用户消息
        :param conversation_history: 对话历史
        :return: AI响应和工具调用结果
        """
        try:
            # 构建消息历史
            messages = []
            
            # 系统提示
            system_prompt = """你是一个专业的证书管理助手，可以帮助用户：
1. 创建证书申请任务
2. 查询任务状态
3. 获取用户或企业信息
4. 生成模拟用户数据

当用户需要这些功能时，请主动调用相应的工具。请用中文回复用户。"""
            
            messages.append({"role": "system", "content": system_prompt})
            
            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            self.logger.info(f"发送消息到DeepSeek: {message}")
            
            # 调用AI模型
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=messages,
                tools=self.available_tools,
                tool_choice="auto",
                temperature=0.7
            )
            
            assistant_message = response.choices[0].message
            tool_calls = assistant_message.tool_calls
            
            result = {
                "message": assistant_message.content,
                "tool_calls": [],
                "tool_results": []
            }
            
            # 处理工具调用
            if tool_calls:
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    self.logger.info(f"调用工具: {function_name}, 参数: {function_args}")
                    
                    # 调用对应的MCP工具
                    tool_result = await self._call_mcp_tool(function_name, function_args)
                    
                    result["tool_calls"].append({
                        "function": function_name,
                        "arguments": function_args
                    })
                    result["tool_results"].append(tool_result)
                
                # 如果有工具调用，让AI总结结果
                if result["tool_results"]:
                    summary_messages = messages + [
                        {"role": "assistant", "content": assistant_message.content},
                        {"role": "user", "content": f"工具调用结果: {json.dumps(result['tool_results'], ensure_ascii=False)}，请总结一下结果。"}
                    ]
                    
                    summary_response = self.client.chat.completions.create(
                        model=settings.DEEPSEEK_MODEL,
                        messages=summary_messages,
                        temperature=0.7
                    )
                    
                    result["message"] = summary_response.choices[0].message.content
            
            self.logger.info(f"AI响应完成")
            return result
            
        except Exception as e:
            self.logger.error(f"AI对话失败: {str(e)}")
            raise
    
    async def _call_mcp_tool(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具

        :param function_name: 函数名
        :param arguments: 参数
        :return: 工具调用结果
        """
        try:
            # 直接调用服务层，避免MCP handlers的异步问题
            if function_name == "create_certificate_task":
                from app.services.certificate_service import CertificateService
                service = CertificateService()
                return await service.create_certificate_task(
                    user_type=arguments.get("user_type", 1),
                    oid=arguments.get("oid"),
                    products=arguments.get("products", [1]),
                    use_old_data=arguments.get("use_old_data", 1)
                )
            elif function_name == "query_task_status":
                from app.services.certificate_service import CertificateService
                service = CertificateService()
                return await service.get_task_status(arguments["task_id"])
            elif function_name == "get_user_info":
                from app.services.user_service import UserService
                service = UserService()
                return await service.get_user_or_enterprise_info(arguments.get("env", 1))
            elif function_name == "generate_mock_user":
                from app.services.user_service import UserService
                service = UserService()
                return await service.generate_mock_user(arguments.get("user_type", "personal"))
            else:
                return {"error": f"未知的工具: {function_name}"}

        except Exception as e:
            self.logger.error(f"调用MCP工具失败: {function_name}, 错误: {str(e)}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    async def simple_chat(self, message: str) -> str:
        """
        简单对话，不使用工具
        
        :param message: 用户消息
        :return: AI回复
        """
        try:
            response = self.client.chat.completions.create(
                model=settings.DEEPSEEK_MODEL,
                messages=[
                    {"role": "system", "content": "你是一个友好的助手，请用中文回复。"},
                    {"role": "user", "content": message}
                ],
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"简单对话失败: {str(e)}")
            raise
