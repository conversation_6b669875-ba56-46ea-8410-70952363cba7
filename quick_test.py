"""
快速测试简化MCP接口
"""
import requests
import json

def test_mcp_api():
    """测试MCP API"""
    base_url = "http://localhost:8001/api/ai/mcp"
    
    print("🧪 测试简化MCP接口...")
    
    # 1. 测试获取工具列表
    print("\n1. 获取工具列表...")
    try:
        response = requests.get(f"{base_url}/tools")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("tools", [])
                print(f"✅ 成功获取 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"   - {tool['name']}")
            else:
                print(f"❌ 获取工具失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 2. 测试调用工具
    print("\n2. 测试调用工具...")
    try:
        payload = {
            "tool_name": "生成模拟用户数据",
            "parameters": {
                "用户类型": "personal"
            }
        }
        
        response = requests.post(
            f"{base_url}/call",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ 工具调用成功!")
                print(f"📄 结果: {json.dumps(data.get('result', {}), ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ 工具调用失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 3. 测试简化MCP信息
    print("\n3. 获取MCP服务信息...")
    try:
        response = requests.get("http://localhost:8001/api/ai/simple-mcp/info")
        if response.status_code == 200:
            data = response.json()
            print("✅ 成功获取MCP服务信息:")
            print(f"   🌐 MCP地址: {data.get('mcp_base_url')}")
            print(f"   📡 工具端点: {data.get('tools_endpoint')}")
            print(f"   🔧 调用端点: {data.get('call_endpoint')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n🎉 测试完成!")
    print("\n💡 使用方法:")
    print("   1. 启动服务: python main.py")
    print("   2. 在AI工具中配置MCP地址: http://localhost:8001/api/ai/mcp")
    print("   3. AI就可以自动调用所有MCP工具了!")

if __name__ == "__main__":
    test_mcp_api()
