"""
测试业务层自动MCP工具生成
========================

验证新的业务层架构是否正常工作
"""
import requests
import json

def test_business_layer():
    """测试业务层自动化MCP工具生成"""
    base_url = "http://localhost:8001/api/ai/mcp"
    
    print("🏗️ 测试业务层自动MCP工具生成")
    print("=" * 60)
    print("🎯 新架构特点：")
    print("   ✅ 业务方法定义在 app/business/ 文件夹")
    print("   ✅ 自动生成MCP工具，无需手动配置")
    print("   ✅ AI可以直接调用业务方法")
    print("   ✅ 完全自动化，即插即用")
    
    # 1. 获取业务工具列表
    print("\n1. 📋 获取业务层自动生成的工具...")
    try:
        response = requests.get(f"{base_url}/tools")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("tools", [])
                print(f"✅ 自动发现了 {len(tools)} 个业务工具:")
                
                # 按模块分组显示
                modules = {}
                for tool in tools:
                    module = tool.get("module", "unknown")
                    if module not in modules:
                        modules[module] = []
                    modules[module].append(tool["name"])
                
                for module, tool_names in modules.items():
                    module_name = module.split('.')[-1] if '.' in module else module
                    print(f"\n   📁 {module_name}:")
                    for tool_name in tool_names:
                        print(f"      - {tool_name}")
                
                print(f"\n🎉 业务层工具来源: {data.get('source', 'unknown')}")
            else:
                print(f"❌ 获取工具失败: {data.get('error')}")
                return
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    # 2. 测试业务工具调用
    print("\n2. 🧪 测试业务工具调用...")
    
    test_cases = [
        {
            "name": "生成手机号",
            "params": {"数量": 2, "运营商": "联通"},
            "description": "测试数据生成工具"
        },
        {
            "name": "获取用户信息", 
            "params": {"环境": 1},
            "description": "测试用户管理工具"
        },
        {
            "name": "生成测试密码",
            "params": {"数量": 3, "长度": 10, "包含特殊字符": True},
            "description": "测试自定义工具"
        },
        {
            "name": "生成完整用户档案",
            "params": {"数量": 1, "地区": "上海"},
            "description": "测试复合数据生成"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   测试: {test_case['description']} - {test_case['name']}")
        try:
            payload = {
                "tool_name": test_case["name"],
                "parameters": test_case["params"]
            }
            
            response = requests.post(
                f"{base_url}/call",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    result = data.get("result", {})
                    print(f"   ✅ 成功！{data.get('message')}")
                    print(f"   📄 来源: {data.get('source')}")
                    
                    # 显示部分结果
                    if result.get("status") == "success":
                        result_data = result.get("data", {})
                        if "手机号列表" in result_data:
                            print(f"      📱 生成的手机号: {result_data['手机号列表']}")
                        elif "密码列表" in result_data:
                            print(f"      🔐 生成的密码: {result_data['密码列表']}")
                        elif "用户档案列表" in result_data:
                            profiles = result_data['用户档案列表']
                            if profiles:
                                print(f"      👤 用户档案: {profiles[0]}")
                    else:
                        print(f"      ⚠️ 结果: {result.get('message', '无详细信息')}")
                else:
                    print(f"   ❌ 调用失败: {data.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 3. 测试AI自动调用业务工具
    print("\n3. 🤖 测试AI自动调用业务工具...")
    
    ai_test_messages = [
        "帮我生成3个移动手机号",
        "生成一个完整的用户档案，地区选北京",
        "帮我生成2个强密码，长度12位",
        "创建一个个人证书任务"
    ]
    
    for message in ai_test_messages:
        print(f"\n   用户: {message}")
        try:
            payload = {
                "message": message,
                "use_tools": True
            }
            
            response = requests.post(
                "http://localhost:8001/api/ai/simple-mcp/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("message"):
                    print(f"   🤖 AI: {data['message'][:100]}...")
                    
                    if data.get("tool_calls"):
                        tools_used = [call['function'] for call in data['tool_calls']]
                        print(f"   🔧 自动调用了业务工具: {tools_used}")
                else:
                    print(f"   ❌ AI对话失败")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 业务层自动化测试完成！")
    
    print("\n🏗️ 新架构总结:")
    print("   📁 app/business/")
    print("      ├── user_tools.py        # 用户相关业务工具")
    print("      ├── certificate_tools.py # 证书相关业务工具") 
    print("      ├── data_generator.py    # 数据生成业务工具")
    print("      └── custom_tools.py      # 自定义业务工具")
    
    print("\n✨ 使用方法:")
    print("   1. 在 app/business/ 任意文件中定义函数")
    print("   2. 添加类型注解和文档字符串")
    print("   3. 重启服务")
    print("   4. AI立即可以使用新的业务工具！")
    
    print("\n🚀 优势:")
    print("   ✅ 完全自动化 - 无需手动配置")
    print("   ✅ 业务分离 - 清晰的代码组织")
    print("   ✅ 即插即用 - 新增函数立即生效")
    print("   ✅ 类型安全 - 自动解析类型注解")
    print("   ✅ 统一接口 - 所有工具使用相同API")

if __name__ == "__main__":
    test_business_layer()
