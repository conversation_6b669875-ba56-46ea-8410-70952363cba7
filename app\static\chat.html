<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>AI 聊天页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        #chat-box { width: 100%; height: 400px; border: 1px solid #ccc; overflow-y: auto; padding: 10px; margin-bottom: 10px; }
        #user-input { width: 80%; padding: 8px; }
        #send-btn { padding: 8px 16px; }
        .msg-user { color: #007bff; margin: 5px 0; }
        .msg-ai { color: #28a745; margin: 5px 0; }
    </style>
</head>
<body>
    <h2>AI 聊天演示</h2>
    <div id="chat-box"></div>
    <input id="user-input" type="text" placeholder="请输入消息..." />
    <button id="send-btn">发送</button>

    <script>
        const chatBox = document.getElementById('chat-box');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');

        function appendMsg(role, text) {
            const div = document.createElement('div');
            div.className = role === 'user' ? 'msg-user' : 'msg-ai';
            div.textContent = (role === 'user' ? '你: ' : 'AI: ') + text;
            chatBox.appendChild(div);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        sendBtn.onclick = async function() {
            const msg = userInput.value.trim();
            if (!msg) return;
            appendMsg('user', msg);
            userInput.value = '';
            appendMsg('ai', '...');
            try {
                const resp = await fetch('/api/ai/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: msg })
                });
                const data = await resp.json();
                chatBox.removeChild(chatBox.lastChild); // 移除 ...
                appendMsg('ai', data.message || '[无回复]');
            } catch (e) {
                chatBox.removeChild(chatBox.lastChild);
                appendMsg('ai', '[请求失败]');
            }
        };
        userInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') sendBtn.onclick();
        });
    </script>
</body>
</html>
