"""
API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8001"

def test_user_api():
    """测试用户API"""
    print("=== 测试用户API ===")
    
    # 测试获取用户信息
    response = requests.get(f"{BASE_URL}/api/users/1")
    print(f"获取用户信息: {response.status_code}")
    if response.status_code == 200:
        print(json.dumps(response.json(), ensure_ascii=False, indent=2))
    else:
        print(f"错误: {response.text}")
    
    # 测试生成模拟用户
    response = requests.get(f"{BASE_URL}/api/users/mock/personal")
    print(f"\n生成模拟用户: {response.status_code}")
    if response.status_code == 200:
        print(json.dumps(response.json(), ensure_ascii=False, indent=2))
    else:
        print(f"错误: {response.text}")

def test_certificate_api():
    """测试证书API"""
    print("\n=== 测试证书API ===")
    
    # 测试创建证书任务
    task_data = {
        "user_type": 1,
        "products": [1],
        "use_old_data": 1
    }
    
    response = requests.post(
        f"{BASE_URL}/api/certificates/tasks",
        json=task_data
    )
    print(f"创建证书任务: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 测试查询任务状态
        task_id = result.get("task_id")
        if task_id:
            response = requests.get(f"{BASE_URL}/api/certificates/tasks/{task_id}/status")
            print(f"\n查询任务状态: {response.status_code}")
            if response.status_code == 200:
                print(json.dumps(response.json(), ensure_ascii=False, indent=2))
            else:
                print(f"错误: {response.text}")
    else:
        print(f"错误: {response.text}")
    
    # 测试获取任务列表
    response = requests.get(f"{BASE_URL}/api/certificates/tasks")
    print(f"\n获取任务列表: {response.status_code}")
    if response.status_code == 200:
        print(json.dumps(response.json(), ensure_ascii=False, indent=2))
    else:
        print(f"错误: {response.text}")

def test_docs():
    """测试API文档"""
    print("\n=== 测试API文档 ===")
    response = requests.get(f"{BASE_URL}/docs")
    print(f"API文档访问: {response.status_code}")

if __name__ == "__main__":
    try:
        test_user_api()
        test_certificate_api()
        test_docs()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试失败: {str(e)}")
