"""
AI API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8002"

def test_ai_health():
    """测试AI服务健康检查"""
    print("=== 测试AI服务健康检查 ===")
    
    response = requests.get(f"{BASE_URL}/api/ai/health")
    print(f"AI健康检查: {response.status_code}")
    if response.status_code == 200:
        print(json.dumps(response.json(), ensure_ascii=False, indent=2))
    else:
        print(f"错误: {response.text}")

def test_available_tools():
    """测试获取可用工具"""
    print("\n=== 测试获取可用工具 ===")
    
    response = requests.get(f"{BASE_URL}/api/ai/tools")
    print(f"获取可用工具: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"工具数量: {result['count']}")
        for i, tool in enumerate(result['tools'], 1):
            print(f"{i}. {tool['name']}: {tool['description']}")
    else:
        print(f"错误: {response.text}")

def test_simple_chat():
    """测试简单对话"""
    print("\n=== 测试简单对话 ===")
    
    data = {
        "message": "你好，请介绍一下你自己"
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/simple-chat", json=data)
    print(f"简单对话: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"AI回复: {result['message']}")
    else:
        print(f"错误: {response.text}")

def test_ai_chat_with_tools():
    """测试AI对话（带工具调用）"""
    print("\n=== 测试AI对话（带工具调用）===")
    
    # 测试1：获取用户信息
    print("\n1. 测试获取用户信息:")
    data = {
        "message": "帮我获取一个测试环境的用户信息",
        "use_tools": True
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/chat", json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"AI回复: {result['message']}")
        if result['tool_calls']:
            print(f"工具调用: {result['tool_calls']}")
        if result['tool_results']:
            print(f"工具结果: {json.dumps(result['tool_results'], ensure_ascii=False, indent=2)}")
    else:
        print(f"错误: {response.text}")
    
    # 测试2：创建证书任务
    print("\n2. 测试创建证书任务:")
    data = {
        "message": "帮我创建一个个人用户的证书申请任务，使用TIANYIN产品",
        "use_tools": True
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/chat", json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"AI回复: {result['message']}")
        if result['tool_calls']:
            print(f"工具调用: {result['tool_calls']}")
        if result['tool_results']:
            print(f"工具结果: {json.dumps(result['tool_results'], ensure_ascii=False, indent=2)}")
            
            # 如果创建成功，尝试查询任务状态
            for tool_result in result['tool_results']:
                if 'task_id' in tool_result:
                    task_id = tool_result['task_id']
                    print(f"\n3. 查询任务状态 (任务ID: {task_id}):")
                    
                    query_data = {
                        "message": f"帮我查询任务ID为{task_id}的状态",
                        "use_tools": True
                    }
                    
                    query_response = requests.post(f"{BASE_URL}/api/ai/chat", json=query_data)
                    if query_response.status_code == 200:
                        query_result = query_response.json()
                        print(f"AI回复: {query_result['message']}")
                        if query_result['tool_results']:
                            print(f"任务状态: {json.dumps(query_result['tool_results'], ensure_ascii=False, indent=2)}")
                    break
    else:
        print(f"错误: {response.text}")

def test_conversation_flow():
    """测试对话流程"""
    print("\n=== 测试对话流程 ===")
    
    conversation_history = []
    
    # 第一轮对话
    print("\n第一轮对话:")
    data = {
        "message": "你好，我想了解一下你能帮我做什么？",
        "use_tools": True,
        "conversation_history": conversation_history
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/chat", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"AI: {result['message']}")
        
        # 更新对话历史
        conversation_history.append({"role": "user", "content": data["message"]})
        conversation_history.append({"role": "assistant", "content": result["message"]})
    
    # 第二轮对话
    print("\n第二轮对话:")
    data = {
        "message": "那帮我生成一个企业类型的模拟用户数据吧",
        "use_tools": True,
        "conversation_history": conversation_history
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/chat", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"AI: {result['message']}")
        if result['tool_results']:
            print(f"生成的数据: {json.dumps(result['tool_results'], ensure_ascii=False, indent=2)}")

def test_docs():
    """测试API文档"""
    print("\n=== 测试API文档 ===")
    response = requests.get(f"{BASE_URL}/docs")
    print(f"API文档访问: {response.status_code}")

if __name__ == "__main__":
    try:
        test_ai_health()
        test_available_tools()
        test_simple_chat()
        test_ai_chat_with_tools()
        test_conversation_flow()
        test_docs()
        print("\n=== AI功能测试完成 ===")
    except Exception as e:
        print(f"测试失败: {str(e)}")
