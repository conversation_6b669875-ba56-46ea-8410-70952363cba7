# 简化MCP使用指南

## 🎯 解决的问题

原来的MCP配置比较复杂：
- 需要修改多个地方
- 嵌套太深，不灵活
- 新增MCP方法需要1小时才能弄好
- 配置繁琐，容易出错

## ✨ 简化方案

现在只需要**一个HTTP地址**就能使用所有MCP功能！

### 核心特点
- 🌐 **统一入口**: 只需配置一个HTTP地址
- 🔍 **自动发现**: 自动发现所有可用工具
- 📝 **统一格式**: 所有工具使用相同的调用格式
- ⚡ **即插即用**: 新增工具自动生效
- 🎯 **简化配置**: 无需修改多个配置文件

## 🚀 快速开始

### 1. 启动服务
```bash
python main.py
```

### 2. 获取可用工具
```bash
curl http://localhost:8000/ai/mcp/tools
```

### 3. 调用MCP工具
```bash
curl -X POST http://localhost:8000/ai/mcp/call \
  -H "Content-Type: application/json" \
  -d '{
    "tool_name": "生成模拟用户数据",
    "parameters": {
      "用户类型": "personal"
    }
  }'
```

### 4. AI对话 + MCP工具
```bash
curl -X POST http://localhost:8000/ai/simple-mcp/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "帮我生成一个个人用户的模拟数据",
    "use_tools": true
  }'
```

## 🎯 在Cursor中使用

### 配置步骤
1. 打开Cursor设置
2. 找到MCP配置
3. 添加MCP服务器地址: `http://localhost:8000/ai/mcp`
4. 保存配置并重启Cursor
5. 现在AI就可以使用所有MCP工具了！

### 配置示例
```json
{
  "mcpServers": {
    "esign-qa-mcp": {
      "url": "http://localhost:8000/ai/mcp",
      "description": "简化的MCP服务 - 证书管理工具"
    }
  }
}
```

## 📋 可用工具

系统会自动发现以下工具：

1. **创建有账号证书申请任务接口** - 创建证书申请任务
2. **根据taskId查询任务状态** - 查询任务状态
3. **获取用户或企业信息** - 获取用户信息
4. **生成模拟用户数据** - 生成测试数据

## 🔧 API接口

### 获取工具列表
- **URL**: `GET /ai/mcp/tools`
- **响应**: 
```json
{
  "success": true,
  "tools": [
    {
      "name": "工具名称",
      "description": "工具描述",
      "parameters": {
        "param1": {
          "type": "string",
          "required": true
        }
      }
    }
  ],
  "count": 4
}
```

### 调用工具
- **URL**: `POST /ai/mcp/call`
- **请求**:
```json
{
  "tool_name": "工具名称",
  "parameters": {
    "param1": "value1",
    "param2": "value2"
  }
}
```
- **响应**:
```json
{
  "success": true,
  "tool_name": "工具名称",
  "parameters": {...},
  "result": {...},
  "message": "成功调用工具"
}
```

### AI对话
- **URL**: `POST /ai/simple-mcp/chat`
- **请求**:
```json
{
  "message": "用户消息",
  "use_tools": true,
  "conversation_history": [...]
}
```

## 🧪 测试

运行测试脚本：
```bash
python test_simple_mcp.py
```

这个脚本会：
1. 获取所有可用工具
2. 测试直接调用工具
3. 测试AI对话 + 工具调用
4. 展示Cursor集成方法

## 💡 使用示例

### Python客户端
```python
import httpx
import asyncio

async def use_mcp():
    async with httpx.AsyncClient() as client:
        # 获取工具列表
        tools = await client.get("http://localhost:8000/ai/mcp/tools")
        print(tools.json())
        
        # 调用工具
        result = await client.post(
            "http://localhost:8000/ai/mcp/call",
            json={
                "tool_name": "生成模拟用户数据",
                "parameters": {"用户类型": "personal"}
            }
        )
        print(result.json())

asyncio.run(use_mcp())
```

### JavaScript客户端
```javascript
// 获取工具列表
const tools = await fetch('http://localhost:8000/ai/mcp/tools');
console.log(await tools.json());

// 调用工具
const result = await fetch('http://localhost:8000/ai/mcp/call', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tool_name: '生成模拟用户数据',
    parameters: { 用户类型: 'personal' }
  })
});
console.log(await result.json());
```

## 🔄 新增工具

要新增MCP工具，只需要：

1. 在 `app/mcp/handlers.py` 中添加新函数
2. 重启服务
3. 工具自动生效，无需其他配置！

示例：
```python
def 新的工具函数(参数1: str, 参数2: int = 1) -> Dict[str, Any]:
    """
    新工具的描述
    
    :param 参数1: 参数1的说明
    :param 参数2: 参数2的说明
    :return: 返回结果
    """
    # 实现逻辑
    return {"result": "success"}
```

## 🎉 总结

通过这个简化方案：
- ✅ 配置从复杂变简单（一个地址搞定）
- ✅ 开发从1小时变1分钟
- ✅ 维护从多处改动变自动发现
- ✅ 集成从繁琐变即插即用

现在AI使用MCP就像访问一个普通的HTTP API一样简单！
