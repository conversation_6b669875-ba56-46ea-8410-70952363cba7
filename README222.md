# fastMcp 项目说明

## 项目简介

本项目基于 [FastAPI](https://fastapi.tiangolo.com/) 和 [fastapi-mcp](https://github.com/tadata-org/fastapi_mcp) 实现，支持自动将 FastAPI 路由暴露为 MCP 工具，便于大模型等系统自动调用。

---

## 目录结构

```
fastMcp/
├── main.py                # FastAPI 主应用入口，包含主要路由和MCP集成
├── app/
│   └── mcp/
│       └── handlers.py    # 业务处理函数（可注册为MCP工具）
├── requireList.txt        # 依赖包列表
├── test.db                # 示例数据库
├── mcp.json               # MCP配置
└── ...
```

---

## 依赖安装

```bash
pip install -r requireList.txt
```

---

## 启动方式

```bash
python main.py
```

服务启动后，默认监听 `http://0.0.0.0:8000/`。

---

## 主要接口说明

### 1. 获取用户或企业信息

- **接口路径**：`GET /users/{env}`
- **参数**：
  - `env` (int): 枚举值，1=模拟环境，2=测试环境
- **返回**：
  - 用户/企业的姓名、手机号、身份证号、企业名称、社会编码、银行卡号等信息

#### 示例

```http
GET /users/1
```

---



## MCP 集成说明

- 项目已集成 fastapi-mcp，所有 FastAPI 路由会自动暴露为 MCP 工具。
- 你可以通过 `/mcp` 路径访问 MCP 服务。

---

## 其他说明

- 你可以在 `app/mcp/handlers.py` 中继续添加更多的业务处理函数。
- 日志已配置为 INFO 级别，输出到控制台。

---

如需扩展接口或MCP工具，只需在 FastAPI 路由或 `handlers.py` 中添加新函数即可。

---

如需更详细的接口文档，可结合 FastAPI 自动生成的 `/docs` (Swagger UI) 页面查看和调试。

--- 