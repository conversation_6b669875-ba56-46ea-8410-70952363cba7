"""
快速AI演示
"""
import requests
import json

BASE_URL = "http://localhost:8002"

def test_ai_features():
    """测试AI功能"""
    
    print("🎉 AI + MCP 集成功能快速演示")
    print("="*50)
    
    # 1. 健康检查
    print("\n1. 健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/api/ai/health", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ 系统状态: {health['status']}")
            print(f"✅ 可用工具: {health['tools_count']} 个")
        else:
            print("❌ 健康检查失败")
            return
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return
    
    # 2. 简单对话
    print("\n2. 简单对话测试...")
    try:
        data = {"message": "你好"}
        response = requests.post(f"{BASE_URL}/api/ai/simple-chat", json=data, timeout=15)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI回复: {result['message'][:100]}...")
        else:
            print(f"❌ 简单对话失败: {response.text}")
    except Exception as e:
        print(f"❌ 简单对话错误: {str(e)}")
    
    # 3. 工具调用测试
    print("\n3. 工具调用测试...")
    try:
        data = {
            "message": "帮我获取一个测试环境的用户信息",
            "use_tools": True
        }
        response = requests.post(f"{BASE_URL}/api/ai/chat", json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI回复: {result['message'][:150]}...")
            if result.get('tool_calls'):
                print(f"✅ 工具调用: {len(result['tool_calls'])} 个")
            if result.get('tool_results'):
                print(f"✅ 工具结果: 获取成功")
        else:
            print(f"❌ 工具调用失败: {response.text}")
    except Exception as e:
        print(f"❌ 工具调用错误: {str(e)}")
    
    print("\n" + "="*50)
    print("🎊 快速演示完成！")
    print("💡 你可以访问 http://localhost:8002/docs 查看完整API文档")
    print("💡 或运行 python demo_ai_mcp.py 进行完整演示")

if __name__ == "__main__":
    test_ai_features()
