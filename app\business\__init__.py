"""
业务层 - 自动MCP工具生成
=========================

🎯 设计目标：
- 在这个文件夹中定义的所有函数都会自动变成MCP工具
- AI可以直接调用这些业务方法
- 无需任何手动配置，完全自动化

📁 目录结构：
app/business/
├── __init__.py          # 业务层初始化
├── user_tools.py        # 用户相关业务工具
├── certificate_tools.py # 证书相关业务工具
├── data_generator.py    # 数据生成业务工具
└── custom_tools.py      # 自定义业务工具

🚀 使用方法：
1. 在任意 .py 文件中定义函数
2. 添加类型注解和文档字符串
3. 重启服务
4. AI立即可以使用新工具！

📝 函数规范：
def 工具名称(参数1: 类型, 参数2: 类型 = 默认值) -> Dict[str, Any]:
    '''
    工具描述
    
    :param 参数1: 参数1的说明
    :param 参数2: 参数2的说明
    :return: 返回结果说明
    '''
    return {"status": "success", "data": "结果"}

✨ 特性：
- 自动类型推断
- 自动参数解析
- 自动错误处理
- 自动日志记录
- 统一返回格式
"""

# 业务层版本信息
__version__ = "1.0.0"
__description__ = "自动化MCP业务工具层"

# 导入所有业务工具模块，实现自动发现
from . import user_tools
from . import certificate_tools  
from . import data_generator
from . import custom_tools

# 业务工具模块列表
BUSINESS_MODULES = [
    user_tools,
    certificate_tools,
    data_generator,
    custom_tools
]

def get_all_business_functions():
    """
    获取所有业务函数

    :return: 业务函数列表
    """
    import inspect

    functions = []
    for module in BUSINESS_MODULES:
        for name, func in inspect.getmembers(module, inspect.isfunction):
            # 跳过私有函数和导入的函数
            if not name.startswith('_') and func.__module__ == module.__name__:
                functions.append((name, func))

    return functions

def get_module_metadata():
    """
    🚀 获取所有模块的元数据

    :return: 模块元数据字典 {module_name: metadata}
    """
    import inspect

    metadata = {}
    for module in BUSINESS_MODULES:
        module_name = module.__name__.split('.')[-1]

        # 尝试获取模块的 MODULE_META
        if hasattr(module, 'MODULE_META'):
            metadata[module_name] = module.MODULE_META
        else:
            # 如果没有定义元数据，使用默认值
            metadata[module_name] = {
                "name": module_name.replace('_', ' ').title(),
                "icon": "📁",
                "description": f"{module_name} 模块",
                "category": "general",
                "ai_prompt_hint": f"当用户需要 {module_name} 相关功能时，请使用此模块的工具"
            }

    return metadata

def get_business_functions_with_metadata():
    """
    🚀 获取带有模块元数据的业务函数

    :return: (functions, metadata) 元组
    """
    functions = get_all_business_functions()
    metadata = get_module_metadata()
    return functions, metadata
