import requests



import logging

# 创建Logger实例
logger = logging.getLogger()
logger.setLevel(logging.INFO)


class TaskStatusException(Exception):
    """自定义任务状态异常"""
    pass


request_headers = {}
env = {"test": "http://cert-service.testk8s.tsign.cn", "sml": "http://cert-service.smlk8s.esign.cn"}


def set_request_headers(app_id=None, group="DEFAULT"):
    global request_headers
    request_headers = {
        "Content-Type": "application/json",
        "X-Tsign-Open-Auth-Mode": "simple",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Service-Group": group
    }


address = ""


def set_url(env_name):
    if "test" in str.lower(env_name) or "测试" in str.lower(env_name):
        env_name = "test"
    elif "sml" in str.lower(env_name) or "moni" in str.lower(env_name) or "pre" in str.lower(env_name) or "模拟" in str.lower(env_name):
        env_name = "sml"
    global address
    address = env[str.lower(env_name)]


def post(url, headers=None, data=None, method="POST"):
    """
    发送HTTP请求并处理响应。

    :param url: 请求的URL
    :param method: 请求方法（GET, POST, PUT, DELETE等）
    :param headers: 请求头（可选）
    :param data: 请求体数据（可选）
    :return: 如果状态码为200，返回响应的JSON对象；否则返回None
    """
    # logging的汉字乱码问题，需要在代码开头添加以下代码
    if headers is None:
        headers = request_headers
    logging.info("发送请求:%s\t%s", method, url)
    logging.info("发送头:%s", str(headers))
    logging.info("请求体: %s", data)
    try:
        # 动态获取 requests 模块中的方法
        request_method = getattr(requests, method.lower(), None)
        if request_method is None:
            return None
        # 发送请求
        if method.upper() in ['POST', 'PUT']:
            response = request_method(url, headers=headers, json=data)
        else:
            response = request_method(url, headers=headers)
        logging.info("返回:%s", response.text)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except requests.exceptions.RequestException as e:
        return None


class CertUtil(object):
    @staticmethod
    def create(cert_name, mobile, license_number, algorithm="SM2", cert_time="ONEYEAR", config_id="**********"):
        url = address + "/openca/rest/cert/createnew"
        data = {
            "certParam": {
                "algorithm": algorithm,
                "certPolicy": "COMMON",
                "certTime": cert_time,
                "certType": "SINGLE",
                "configId": config_id,
                "isUkey": False,
                "issuer": "ZHCA"
            },
            "commonParam": {
                "address": "天堂软件园",
                "mobile": mobile,
                "phone": mobile
            },
            "userParam": {
                "certName": cert_name,
                "licenseNumber": license_number,
                "licenseType": 19
            }
        }
        response = post(url, data=data)
        return response

    @staticmethod
    def detail(cert_info_id, config_id="**********"):
        url = address + "/openca/rest/cert/detail"
        data = {
            "certInfoId": cert_info_id,
            "projectId": config_id
        }
        response = post(url, data=data)
        return response

    @staticmethod
    def revoke(cert_info_id):
        url = address + "/openca/rest/cert/revoke"
        data = {
            "certInfoId": cert_info_id
        }
        response = post(url, data=data)
        return response

    @staticmethod
    def update(cert_info_id, cert_name, mobile, license_number, algorithm="SM2", cert_time="ONEYEAR",
               config_id="**********"):
        url = address + "/openca/rest/cert/update"
        data = {
            "certInfoId": cert_info_id,
            "certParam": {
                "algorithm": algorithm,
                "certPolicy": "COMMON",
                "certTime": cert_time,
                "certType": "SINGLE",
                "configId": config_id,
                "isUkey": False,
                "issuer": "ZHCA"
            },
            "commonParam": {
                "address": "天堂软件园",
                "mobile": mobile,
                "phone": mobile
            },
            "userParam": {
                "certName": cert_name,
                "licenseNumber": license_number,
                "licenseType": 19
            }
        }
        response = post(url, data=data)
        return response

    @staticmethod
    def get_account():
        url = "http://sdk.testk8s.tsign.cn/random/get"
        data = {
            "env": "测试环境",
            "mock": False,
            "total": 1
        }
        response = post(url, data=data)
        return response["accountList"][0]


if __name__ == "__main__":
    set_request_headers(app_id="", group="DEFAULT")
    set_url("test")
    person_old = CertUtil.get_account()
    person_new = CertUtil.get_account()
    create_response = CertUtil.create(person_old["name"], person_old["phone"], person_old["idNo"])
    logging.info("创建证书结果: %s", create_response["signCert"])
    detail_response = CertUtil.detail(create_response["certInfoId"])
    logging.info("证书详情为: %s", str(detail_response["certInfo"]))
    logging.info("账号姓名为: %s", str(person_old["name"]))
    logging.info("证书名字为: %s", str(detail_response["certInfo"]["certname"]))
    assert str(detail_response["certInfo"]["certname"]) == str(
        person_old["name"]), "证书名字与账号姓名不相同，预期为：{}，实际为：{}".format(person_old["name"],
                                                                                   detail_response["certInfo"][
                                                                                       "certname"])
    update_response = CertUtil.update(create_response["certInfoId"], person_new["name"], person_new["phone"],
                                      person_new["idNo"])
    detail_response = CertUtil.detail(create_response["certInfoId"])
    logging.info("更新证书后，查询详情返回: %s", str(detail_response["certInfo"]))
    logging.info("更新账号姓名为: %s", str(person_new["name"]))
    logging.info("更新证书后，证书名字为: %s", str(detail_response["certInfo"]["certname"]))
    assert str(detail_response["certInfo"]["certname"]) == str(
        person_new["name"]), "更新证书后，证书名字未更新，预期为：{}，实际为：{}".format(person_new["name"],
                                                                                    detail_response["certInfo"][
                                                                                        "certname"])
    revoke_response = CertUtil.revoke(create_response["certInfoId"])
    logging.info("吊销证书结果: %s", revoke_response)
    detail_response = CertUtil.detail(create_response["certInfoId"])
    assert detail_response["errCode"] == 850007, "注销证书后，查询证书应当返回：{}，实际返回为：{}".format(850007,
                                                                                                       detail_response[
                                                                                                           "errCode"])
    assert detail_response["msg"] == "证书记录不存在", "注销证书后，查询证书应当返回：{}，实际返回为：{}".format(
        "证书记录不存在",
        detail_response[
            "msg"])
