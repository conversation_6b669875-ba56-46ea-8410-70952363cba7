# FastMCP 架构说明

## 项目结构

```
fastMcp/
├── main.py                 # 应用启动入口（仅负责启动）
├── app/
│   ├── __init__.py
│   ├── core/               # 核心配置
│   │   ├── __init__.py
│   │   ├── app.py         # 应用创建和配置
│   │   └── config.py      # 配置管理
│   ├── api/               # API路由层
│   │   ├── __init__.py
│   │   ├── routes.py      # 路由汇总
│   │   └── endpoints/     # 具体端点
│   │       ├── __init__.py
│   │       ├── users.py   # 用户相关API
│   │       └── certificates.py # 证书相关API
│   ├── services/          # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── base_service.py      # 基础服务类
│   │   ├── user_service.py      # 用户业务逻辑
│   │   └── certificate_service.py # 证书业务逻辑
│   ├── schemas/           # 数据模型
│   │   ├── __init__.py
│   │   ├── user_schemas.py      # 用户数据模型
│   │   └── certificate_schemas.py # 证书数据模型
│   ├── models/            # 数据库模型（预留）
│   ├── crud/              # 数据库操作（预留）
│   └── mcp/               # MCP相关功能
│       ├── __init__.py
│       └── handlers.py    # MCP工具处理器
├── tests/                 # 测试文件
└── requirements.txt       # 依赖管理
```

## 架构层次

### 1. 启动层 (main.py)
- **职责**: 仅负责应用启动和基础配置
- **原则**: 不包含任何业务逻辑

### 2. 核心层 (app/core/)
- **app.py**: 应用实例创建、路由注册、MCP配置
- **config.py**: 统一配置管理，支持环境变量

### 3. API层 (app/api/)
- **routes.py**: 路由汇总，统一管理所有API路由
- **endpoints/**: 具体的API端点实现
  - 只负责参数验证、调用服务层、返回响应
  - 不包含业务逻辑

### 4. 业务逻辑层 (app/services/)
- **base_service.py**: 基础服务类，提供通用功能
- **user_service.py**: 用户相关业务逻辑
- **certificate_service.py**: 证书相关业务逻辑
- **职责**: 核心业务逻辑处理、外部API调用、数据处理

### 5. 数据模型层 (app/schemas/)
- **user_schemas.py**: 用户相关的Pydantic模型
- **certificate_schemas.py**: 证书相关的Pydantic模型
- **职责**: 数据验证、序列化、文档生成

### 6. MCP层 (app/mcp/)
- **handlers.py**: MCP工具处理器
- **职责**: 将MCP工具调用转发给服务层

## 业务模块分类

### 当前业务模块
1. **用户管理** (User Management)
   - 获取用户/企业信息
   - 生成模拟用户数据

2. **证书管理** (Certificate Management)
   - 创建证书申请任务
   - 查询任务状态
   - 获取任务列表

### 扩展业务模块示例
3. **订单管理** (Order Management)
4. **支付管理** (Payment Management)
5. **通知管理** (Notification Management)

## 添加新业务模块的步骤

### 1. 创建数据模型
```python
# app/schemas/new_business_schemas.py
from pydantic import BaseModel

class NewBusinessRequest(BaseModel):
    # 定义请求模型
    pass

class NewBusinessResponse(BaseModel):
    # 定义响应模型
    pass
```

### 2. 创建业务服务
```python
# app/services/new_business_service.py
from app.services.base_service import BaseService

class NewBusinessService(BaseService):
    async def some_business_method(self):
        # 实现业务逻辑
        pass
```

### 3. 创建API端点
```python
# app/api/endpoints/new_business.py
from fastapi import APIRouter
from app.services.new_business_service import NewBusinessService

router = APIRouter()

@router.post("/")
async def create_something():
    service = NewBusinessService()
    return await service.some_business_method()
```

### 4. 注册路由
```python
# app/api/routes.py
from app.api.endpoints import new_business

api_router.include_router(
    new_business.router, 
    prefix="/new-business", 
    tags=["新业务模块"]
)
```

### 5. 添加MCP工具（可选）
```python
# app/mcp/handlers.py
def 新业务工具():
    service = NewBusinessService()
    return asyncio.run(service.some_business_method())
```

## 优势

1. **职责分离**: 每层都有明确的职责，便于维护
2. **可扩展性**: 新增业务模块只需按模板添加
3. **可测试性**: 每层都可以独立测试
4. **配置统一**: 所有配置集中管理
5. **代码复用**: 基础服务类提供通用功能
6. **类型安全**: 使用Pydantic进行数据验证

## 注意事项

1. 服务层方法使用async/await，提高并发性能
2. MCP工具由于框架限制使用asyncio.run()调用异步方法
3. 所有外部API调用都通过base_service统一处理
4. 错误处理在每层都有相应的处理机制
5. 日志记录贯穿各个层次，便于调试和监控
