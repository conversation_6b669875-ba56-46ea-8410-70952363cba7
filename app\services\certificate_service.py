"""
证书相关业务服务
"""
from typing import Dict, Any, List, Optional
import uuid
import datetime
from app.services.base_service import BaseService

# 全局任务存储（实际项目中应该使用数据库）
_GLOBAL_TASKS = {}

class CertificateService(BaseService):
    """证书服务类，处理证书相关的业务逻辑"""

    def __init__(self):
        super().__init__()
        # 使用全局任务存储
        self.tasks = _GLOBAL_TASKS
    
    async def create_certificate_task(
        self, 
        user_type: int, 
        oid: Optional[str] = None, 
        products: List[int] = None, 
        use_old_data: int = 1
    ) -> Dict[str, Any]:
        """
        创建证书申请任务
        
        :param user_type: 用户类型 1-个人 2-企业
        :param oid: 用户OID
        :param products: 产品列表
        :param use_old_data: 是否使用老数据
        :return: 任务创建结果
        """
        try:
            if products is None:
                products = [1]  # 默认TIANYIN
            
            # 生成任务ID
            task_id = f"cert_task_{uuid.uuid4().hex[:12]}"
            
            # 创建任务记录
            task_data = {
                "task_id": task_id,
                "user_type": user_type,
                "oid": oid,
                "products": products,
                "use_old_data": use_old_data,
                "status": "created",
                "progress": 0,
                "created_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat(),
                "details": "任务已创建，等待处理"
            }
            
            # 存储任务（实际项目中应该存储到数据库）
            self.tasks[task_id] = task_data
            
            self.logger.info(f"创建证书任务成功: {task_id}")
            
            return {
                "status": "success",
                "task_id": task_id,
                "message": "证书任务创建成功",
                "details": {
                    "user_type": "个人" if user_type == 1 else "企业",
                    "products": [self._get_product_name(p) for p in products],
                    "created_at": task_data["created_at"]
                }
            }
            
        except Exception as e:
            self.logger.error(f"创建证书任务失败: {str(e)}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        查询任务状态
        
        :param task_id: 任务ID
        :return: 任务状态信息
        """
        try:
            if task_id not in self.tasks:
                raise Exception(f"任务不存在: {task_id}")
            
            task = self.tasks[task_id]
            
            # 模拟任务进度更新
            if task["status"] == "created":
                task["status"] = "processing"
                task["progress"] = 50
                task["details"] = "任务处理中"
                task["updated_at"] = datetime.datetime.now().isoformat()
            elif task["status"] == "processing":
                task["status"] = "completed"
                task["progress"] = 100
                task["details"] = "任务已完成"
                task["updated_at"] = datetime.datetime.now().isoformat()
            
            self.logger.info(f"查询任务状态: {task_id} - {task['status']}")
            
            return {
                "status": task["status"],
                "task_id": task_id,
                "details": task["details"],
                "progress": task["progress"],
                "created_at": task["created_at"],
                "updated_at": task["updated_at"]
            }
            
        except Exception as e:
            self.logger.error(f"查询任务状态失败: {str(e)}")
            raise
    
    async def get_task_list(
        self, 
        status: Optional[str] = None, 
        limit: int = 10, 
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        获取任务列表
        
        :param status: 状态过滤
        :param limit: 限制数量
        :param offset: 偏移量
        :return: 任务列表
        """
        try:
            tasks = list(self.tasks.values())
            
            # 状态过滤
            if status:
                tasks = [task for task in tasks if task["status"] == status]
            
            # 排序（按创建时间倒序）
            tasks.sort(key=lambda x: x["created_at"], reverse=True)
            
            # 分页
            total = len(tasks)
            tasks = tasks[offset:offset + limit]
            
            # 格式化返回数据
            items = []
            for task in tasks:
                items.append({
                    "status": task["status"],
                    "task_id": task["task_id"],
                    "details": task["details"],
                    "progress": task["progress"],
                    "created_at": task["created_at"],
                    "updated_at": task["updated_at"]
                })
            
            self.logger.info(f"获取任务列表: 总数{total}, 返回{len(items)}条")
            
            return {
                "total": total,
                "items": items,
                "limit": limit,
                "offset": offset
            }
            
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            raise
    
    def _get_product_name(self, product_id: int) -> str:
        """获取产品名称"""
        product_names = {
            1: "TIANYIN",
            2: "TCLOUD", 
            3: "ESHIELD"
        }
        return product_names.get(product_id, f"Unknown({product_id})")
