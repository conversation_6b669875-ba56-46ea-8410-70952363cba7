"""
AI相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="角色: user/assistant/system")
    content: str = Field(..., description="消息内容")

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息")
    conversation_history: Optional[List[ChatMessage]] = Field(None, description="对话历史")
    use_tools: bool = Field(True, description="是否使用工具")

class ToolCall(BaseModel):
    """工具调用模型"""
    function: str = Field(..., description="函数名")
    arguments: Dict[str, Any] = Field(..., description="函数参数")

class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str = Field(..., description="AI回复消息")
    tool_calls: List[ToolCall] = Field([], description="工具调用列表")
    tool_results: List[Dict[str, Any]] = Field([], description="工具调用结果")
    conversation_id: Optional[str] = Field(None, description="对话ID")

class SimpleChatRequest(BaseModel):
    """简单聊天请求模型"""
    message: str = Field(..., description="用户消息")

class SimpleChatResponse(BaseModel):
    """简单聊天响应模型"""
    message: str = Field(..., description="AI回复消息")

class AvailableToolsResponse(BaseModel):
    """可用工具响应模型"""
    tools: List[Dict[str, Any]] = Field(..., description="可用工具列表")
    count: int = Field(..., description="工具数量")
