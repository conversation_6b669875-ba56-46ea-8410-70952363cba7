"""
AI + MCP 功能演示脚本
展示完整的AI对话和工具调用流程
"""
import requests
import json
import time

BASE_URL = "http://localhost:8002"

def print_separator(title):
    """打印分隔符"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def ai_chat(message, use_tools=True):
    """AI对话函数"""
    data = {
        "message": message,
        "use_tools": use_tools
    }
    
    print(f"👤 用户: {message}")
    print("🤖 AI正在思考...")
    
    response = requests.post(f"{BASE_URL}/api/ai/chat", json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"🤖 AI: {result['message']}")
        
        if result.get('tool_calls'):
            print(f"\n🔧 工具调用: {len(result['tool_calls'])} 个")
            for i, tool_call in enumerate(result['tool_calls'], 1):
                print(f"   {i}. {tool_call['function']} - {tool_call['arguments']}")
        
        if result.get('tool_results'):
            print(f"\n📊 工具结果:")
            for i, tool_result in enumerate(result['tool_results'], 1):
                print(f"   结果 {i}: {json.dumps(tool_result, ensure_ascii=False, indent=4)}")
        
        return result
    else:
        print(f"❌ 错误: {response.text}")
        return None

def demo_complete_workflow():
    """演示完整的业务流程"""
    
    print_separator("🎉 AI + MCP 集成功能演示")
    
    # 1. 健康检查
    print_separator("1. 系统健康检查")
    response = requests.get(f"{BASE_URL}/api/ai/health")
    if response.status_code == 200:
        health = response.json()
        print(f"✅ 系统状态: {health['status']}")
        print(f"✅ AI服务: {health['ai_service']}")
        print(f"✅ 可用工具: {health['tools_count']} 个")
    else:
        print("❌ 系统健康检查失败")
        return
    
    # 2. 获取可用工具
    print_separator("2. 查看可用工具")
    response = requests.get(f"{BASE_URL}/api/ai/tools")
    if response.status_code == 200:
        tools = response.json()
        print(f"📋 共有 {tools['count']} 个可用工具:")
        for i, tool in enumerate(tools['tools'], 1):
            print(f"   {i}. {tool['name']}: {tool['description']}")
    
    # 3. 简单对话测试
    print_separator("3. 简单对话测试")
    data = {"message": "你好，你能帮我做什么？"}
    response = requests.post(f"{BASE_URL}/api/ai/simple-chat", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"👤 用户: {data['message']}")
        print(f"🤖 AI: {result['message'][:200]}...")
    
    # 4. 智能对话 - 获取用户信息
    print_separator("4. 智能对话 - 获取用户信息")
    user_result = ai_chat("帮我获取一个测试环境的用户信息")
    
    # 5. 智能对话 - 生成模拟数据
    print_separator("5. 智能对话 - 生成模拟数据")
    ai_chat("帮我生成一个企业类型的模拟用户数据")
    
    # 6. 智能对话 - 创建证书任务
    print_separator("6. 智能对话 - 创建证书任务")
    task_result = ai_chat("帮我创建一个个人用户的证书申请任务，使用TIANYIN产品")
    
    # 7. 智能对话 - 查询任务状态
    if task_result and task_result.get('tool_results'):
        for tool_result in task_result['tool_results']:
            if 'task_id' in tool_result:
                task_id = tool_result['task_id']
                print_separator("7. 智能对话 - 查询任务状态")
                ai_chat(f"帮我查询任务ID为{task_id}的状态")
                break
    
    # 8. 复杂对话流程
    print_separator("8. 复杂对话流程")
    ai_chat("我想为一个企业用户创建证书任务，但是我需要先获取用户信息，然后创建任务，最后查询状态，你能帮我完成这个流程吗？")
    
    # 9. 业务咨询
    print_separator("9. 业务咨询")
    ai_chat("请介绍一下证书申请的流程和注意事项")
    
    print_separator("🎊 演示完成")
    print("✨ AI + MCP 集成功能演示成功完成！")
    print("✨ 你现在可以通过自然语言与AI对话来完成各种证书管理任务")
    print("✨ 无需通过Cursor等编辑器，直接使用HTTP API即可")

def interactive_demo():
    """交互式演示"""
    print_separator("🎮 交互式AI助手")
    print("💡 你可以尝试以下指令:")
    print("   - 获取用户信息")
    print("   - 创建证书任务") 
    print("   - 查询任务状态")
    print("   - 生成模拟数据")
    print("   - 或者任何其他问题")
    print("💡 输入 'quit' 退出")
    
    while True:
        try:
            user_input = input("\n👤 你: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if user_input:
                ai_chat(user_input)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    try:
        # 检查服务是否可用
        response = requests.get(f"{BASE_URL}/api/ai/health", timeout=30)
        if response.status_code != 200:
            print("❌ 服务不可用，请确保服务器正在运行在端口8002")
            exit(1)
        
        print("🚀 欢迎使用 AI + MCP 集成功能演示")
        print("📍 服务地址:", BASE_URL)
        
        choice = input("\n请选择演示模式:\n1. 完整演示 (推荐)\n2. 交互式对话\n请输入选择 (1/2): ").strip()
        
        if choice == "1":
            demo_complete_workflow()
        elif choice == "2":
            interactive_demo()
        else:
            print("运行完整演示...")
            demo_complete_workflow()
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行在端口8002")
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
