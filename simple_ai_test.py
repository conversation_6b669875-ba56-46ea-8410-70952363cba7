"""
简单AI测试
"""
import requests
import json

BASE_URL = "http://localhost:8002"

def test_simple_chat():
    """测试简单对话"""
    print("=== 测试简单对话 ===")
    
    data = {
        "message": "你好，请介绍一下你自己"
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/simple-chat", json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"AI回复: {result['message']}")
    else:
        print(f"错误: {response.text}")

def test_ai_with_tools():
    """测试带工具的AI对话"""
    print("\n=== 测试AI对话（带工具）===")
    
    data = {
        "message": "帮我获取一个测试环境的用户信息",
        "use_tools": True
    }
    
    response = requests.post(f"{BASE_URL}/api/ai/chat", json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"AI回复: {result['message']}")
        if result.get('tool_calls'):
            print(f"工具调用: {result['tool_calls']}")
        if result.get('tool_results'):
            print(f"工具结果: {json.dumps(result['tool_results'], ensure_ascii=False, indent=2)}")
    else:
        print(f"错误: {response.text}")

if __name__ == "__main__":
    test_simple_chat()
    test_ai_with_tools()
