"""
测试简化的MCP接口
演示如何通过一个HTTP地址使用所有MCP功能
"""
import asyncio
import httpx
import json
from typing import Dict, Any


class SimpleMCPClient:
    """简化的MCP客户端 - 演示如何轻松使用MCP"""
    
    def __init__(self, base_url: str = "http://localhost:8001/api/ai/mcp"):
        self.base_url = base_url
    
    async def get_tools(self) -> Dict[str, Any]:
        """获取所有可用工具"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/tools")
            return response.json()
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        async with httpx.AsyncClient() as client:
            payload = {
                "tool_name": tool_name,
                "parameters": parameters
            }
            response = await client.post(
                f"{self.base_url}/call",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            return response.json()
    
    async def chat_with_mcp(self, message: str) -> Dict[str, Any]:
        """使用简化的MCP对话接口"""
        async with httpx.AsyncClient() as client:
            payload = {
                "message": message,
                "use_tools": True
            }
            response = await client.post(
                "http://localhost:8001/api/ai/simple-mcp/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            return response.json()


async def test_simple_mcp():
    """测试简化的MCP功能"""
    print("🚀 开始测试简化的MCP接口...")
    
    client = SimpleMCPClient()
    
    try:
        # 1. 获取工具列表
        print("\n📋 1. 获取可用工具列表...")
        tools_response = await client.get_tools()
        
        if tools_response.get("success"):
            tools = tools_response.get("tools", [])
            print(f"✅ 发现 {len(tools)} 个可用工具:")
            for tool in tools:
                print(f"   - {tool['name']}: {tool['description']}")
        else:
            print(f"❌ 获取工具失败: {tools_response.get('error')}")
            return
        
        # 2. 测试直接调用工具
        print("\n🔧 2. 测试直接调用MCP工具...")
        
        # 测试生成模拟用户数据
        print("   测试生成模拟用户数据...")
        result = await client.call_tool(
            "生成模拟用户数据",
            {"用户类型": "personal"}
        )
        
        if result.get("success"):
            print("   ✅ 工具调用成功!")
            print(f"   📄 结果: {json.dumps(result.get('result', {}), ensure_ascii=False, indent=2)}")
        else:
            print(f"   ❌ 工具调用失败: {result.get('error')}")
        
        # 3. 测试AI对话 + MCP工具
        print("\n🤖 3. 测试AI对话 + MCP工具调用...")
        
        chat_messages = [
            "帮我生成一个个人用户的模拟数据",
            "帮我创建一个个人证书申请任务",
            "获取一些测试环境的用户信息"
        ]
        
        for message in chat_messages:
            print(f"\n   用户: {message}")
            
            chat_result = await client.chat_with_mcp(message)
            
            if chat_result.get("message"):
                print(f"   🤖 AI: {chat_result['message']}")
                
                if chat_result.get("tool_calls"):
                    print(f"   🔧 调用了 {len(chat_result['tool_calls'])} 个工具:")
                    for tool_call in chat_result["tool_calls"]:
                        print(f"      - {tool_call['function']}")
            else:
                print(f"   ❌ 对话失败")
            
            # 稍微等待一下
            await asyncio.sleep(1)
        
        print("\n🎉 简化MCP测试完成!")
        print("\n💡 总结:")
        print("   ✅ 只需要一个HTTP地址就能使用所有MCP功能")
        print("   ✅ 自动发现所有可用工具，无需手动配置")
        print("   ✅ 统一的调用格式，简化集成")
        print("   ✅ AI可以自动调用工具完成复杂任务")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


async def demo_cursor_integration():
    """演示如何在Cursor中配置MCP"""
    print("\n🎯 Cursor MCP 集成演示")
    print("=" * 50)
    
    # 获取服务信息
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8001/api/ai/simple-mcp/info")
            info = response.json()
            
            print("📋 MCP服务配置信息:")
            print(f"   🌐 MCP地址: {info['mcp_base_url']}")
            print(f"   📡 工具列表: {info['tools_endpoint']}")
            print(f"   🔧 调用接口: {info['call_endpoint']}")
            
            print("\n🎯 在Cursor中的配置:")
            print("   1. 打开Cursor设置")
            print("   2. 找到MCP配置")
            print(f"   3. 添加MCP服务器: {info['mcp_base_url']}")
            print("   4. 保存配置")
            print("   5. 重启Cursor")
            print("   6. 现在AI就可以使用所有MCP工具了!")
            
            print("\n💡 优势:")
            for benefit in info.get("benefits", []):
                print(f"   ✅ {benefit}")
                
    except Exception as e:
        print(f"❌ 获取配置信息失败: {str(e)}")


async def main():
    """主函数"""
    print("🔥 简化MCP使用演示")
    print("=" * 50)
    print("这个演示展示了如何通过一个HTTP地址轻松使用MCP功能")
    print("解决了原来配置复杂、嵌套太深的问题")
    
    # 测试简化MCP
    await test_simple_mcp()
    
    # 演示Cursor集成
    await demo_cursor_integration()
    
    print("\n🚀 现在你可以:")
    print("   1. 启动服务: python main.py")
    print("   2. 在AI工具中配置MCP地址: http://localhost:8001/api/ai/mcp")
    print("   3. 开始使用所有MCP功能!")


if __name__ == "__main__":
    asyncio.run(main())
