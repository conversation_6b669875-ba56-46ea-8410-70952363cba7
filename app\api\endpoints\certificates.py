"""
证书相关API端点
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List, Optional
import logging

from app.services.certificate_service import CertificateService
from app.schemas.certificate_schemas import (
    CertificateTaskRequest, 
    CertificateTaskResponse,
    TaskStatusResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/tasks",
            description="创建证书申请任务",
            response_model=CertificateTaskResponse)
async def create_certificate_task(request: CertificateTaskRequest) -> Dict[str, Any]:
    """
    创建证书申请任务
    
    :param request: 证书任务请求参数
    :return: 任务创建结果
    """
    try:
        certificate_service = CertificateService()
        result = await certificate_service.create_certificate_task(
            user_type=request.user_type,
            oid=request.oid,
            products=request.products,
            use_old_data=request.use_old_data
        )
        return result
    except Exception as e:
        logger.error(f"创建证书任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建证书任务失败: {str(e)}")

@router.get("/tasks/{task_id}/status",
           description="查询任务状态",
           response_model=TaskStatusResponse)
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    查询任务状态
    
    :param task_id: 任务ID
    :return: 任务状态信息
    """
    try:
        certificate_service = CertificateService()
        result = await certificate_service.get_task_status(task_id)
        return result
    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")

@router.get("/tasks",
           description="获取任务列表")
async def get_task_list(
    status: Optional[str] = None,
    limit: int = 10,
    offset: int = 0
) -> Dict[str, Any]:
    """
    获取任务列表
    
    :param status: 任务状态过滤
    :param limit: 返回数量限制
    :param offset: 偏移量
    :return: 任务列表
    """
    try:
        certificate_service = CertificateService()
        result = await certificate_service.get_task_list(status, limit, offset)
        return result
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")
